"""
对话记录器 - 详细记录AI对话过程，便于调试和优化
"""

import json
import logging
import os
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, List, Optional
from dataclasses import dataclass, asdict
from enum import Enum

logger = logging.getLogger(__name__)


class ConversationStatus(str, Enum):
    """对话状态"""
    STARTED = "started"
    IN_PROGRESS = "in_progress"
    SUCCESS = "success"
    FAILED = "failed"
    RETRIED = "retried"
    FALLBACK = "fallback"


@dataclass
class ConversationRound:
    """单轮对话记录"""
    round_number: int
    round_name: str
    prompt: str
    response: str
    model_name: str
    timestamp: str
    duration: float
    status: ConversationStatus
    retry_count: int = 0
    error_message: Optional[str] = None
    token_usage: Optional[Dict[str, int]] = None
    model_config: Optional[Dict[str, Any]] = None


@dataclass
class ConversationSession:
    """完整对话会话记录"""
    session_id: str
    task_id: str
    task_title: str
    task_type: str
    project_path: str
    started_at: str
    ended_at: Optional[str] = None
    total_duration: Optional[float] = None
    status: ConversationStatus = ConversationStatus.STARTED
    rounds: List[ConversationRound] = None
    final_result: Optional[str] = None
    error_summary: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None

    def __post_init__(self):
        if self.rounds is None:
            self.rounds = []


class ConversationLogger:
    """
    对话记录器

    功能：
    1. 记录每轮AI对话的详细信息
    2. 保存到结构化的日志文件
    3. 提供查询和分析接口
    4. 支持不同格式的导出
    """

    def __init__(self, log_dir: str = None):
        """初始化对话记录器"""
        if log_dir is None:
            log_dir = os.path.join(os.getcwd(), "logs", "conversations")

        self.log_dir = Path(log_dir)
        self.log_dir.mkdir(parents=True, exist_ok=True)

        # 当前活跃的会话
        self.active_sessions: Dict[str, ConversationSession] = {}

        logger.info(f"ConversationLogger initialized, log_dir: {self.log_dir}")

    def start_session(self, task_id: str, task_title: str, task_type: str,
                     project_path: str, metadata: Dict[str, Any] = None) -> str:
        """
        开始新的对话会话

        Args:
            task_id: 任务ID
            task_title: 任务标题
            task_type: 任务类型
            project_path: 项目路径
            metadata: 额外元数据

        Returns:
            str: 会话ID
        """
        session_id = f"{task_id}_{int(time.time())}"

        session = ConversationSession(
            session_id=session_id,
            task_id=task_id,
            task_title=task_title,
            task_type=task_type,
            project_path=project_path,
            started_at=datetime.now().isoformat(),
            metadata=metadata or {}
        )

        self.active_sessions[session_id] = session

        logger.info(f"Started conversation session: {session_id}")
        return session_id

    def log_round(self, session_id: str, round_number: int, round_name: str,
                  prompt: str, response: str, model_name: str, duration: float,
                  status: ConversationStatus = ConversationStatus.SUCCESS,
                  retry_count: int = 0, error_message: str = None,
                  token_usage: Dict[str, int] = None,
                  model_config: Dict[str, Any] = None):
        """
        记录单轮对话

        Args:
            session_id: 会话ID
            round_number: 轮次编号
            round_name: 轮次名称
            prompt: 输入提示
            response: AI响应
            model_name: 模型名称
            duration: 执行时长
            status: 对话状态
            retry_count: 重试次数
            error_message: 错误信息
            token_usage: Token使用情况
            model_config: 模型配置
        """
        if session_id not in self.active_sessions:
            logger.warning(f"Session {session_id} not found")
            return

        conversation_round = ConversationRound(
            round_number=round_number,
            round_name=round_name,
            prompt=prompt,
            response=response,
            model_name=model_name,
            timestamp=datetime.now().isoformat(),
            duration=duration,
            status=status,
            retry_count=retry_count,
            error_message=error_message,
            token_usage=token_usage,
            model_config=model_config
        )

        session = self.active_sessions[session_id]
        session.rounds.append(conversation_round)
        session.status = ConversationStatus.IN_PROGRESS

        # 实时保存到文件
        self._save_session_to_file(session)

        logger.debug(f"Logged round {round_number} for session {session_id}")

    def end_session(self, session_id: str, final_result: str = None,
                   status: ConversationStatus = ConversationStatus.SUCCESS,
                   error_summary: str = None):
        """
        结束对话会话

        Args:
            session_id: 会话ID
            final_result: 最终结果
            status: 最终状态
            error_summary: 错误摘要
        """
        if session_id not in self.active_sessions:
            logger.warning(f"Session {session_id} not found")
            return

        session = self.active_sessions[session_id]
        session.ended_at = datetime.now().isoformat()
        session.status = status
        session.final_result = final_result
        session.error_summary = error_summary

        # 计算总时长
        if session.started_at:
            start_time = datetime.fromisoformat(session.started_at)
            end_time = datetime.fromisoformat(session.ended_at)
            session.total_duration = (end_time - start_time).total_seconds()

        # 保存最终版本
        self._save_session_to_file(session)

        # 从活跃会话中移除
        del self.active_sessions[session_id]

        logger.info(f"Ended conversation session: {session_id}, status: {status}")

    def _save_session_to_file(self, session: ConversationSession):
        """保存会话到文件"""
        try:
            # 按日期组织文件
            date_str = datetime.now().strftime("%Y-%m-%d")
            date_dir = self.log_dir / date_str
            date_dir.mkdir(exist_ok=True)

            # JSON格式保存
            json_file = date_dir / f"{session.session_id}.json"
            with open(json_file, 'w', encoding='utf-8') as f:
                json.dump(asdict(session), f, ensure_ascii=False, indent=2)

            # Markdown格式保存（便于阅读）
            md_file = date_dir / f"{session.session_id}.md"
            with open(md_file, 'w', encoding='utf-8') as f:
                f.write(self._format_session_as_markdown(session))

        except Exception as e:
            logger.error(f"Failed to save session {session.session_id}: {e}")

    def _format_session_as_markdown(self, session: ConversationSession) -> str:
        """将会话格式化为Markdown"""
        md_content = f"""# 对话会话记录

## 📋 会话信息
- **会话ID**: {session.session_id}
- **任务ID**: {session.task_id}
- **任务标题**: {session.task_title}
- **任务类型**: {session.task_type}
- **项目路径**: {session.project_path}
- **开始时间**: {session.started_at}
- **结束时间**: {session.ended_at or '进行中'}
- **总时长**: {f'{session.total_duration:.2f}秒' if session.total_duration else '计算中'}
- **最终状态**: {session.status.value}

## 🔄 对话轮次

"""

        # 按轮次编号排序，确保显示顺序正确
        sorted_rounds = sorted(session.rounds, key=lambda r: r.round_number)

        for round_info in sorted_rounds:
            md_content += f"""### 第{round_info.round_number}轮: {round_info.round_name}

**时间**: {round_info.timestamp}
**模型**: {round_info.model_name}
**时长**: {round_info.duration:.2f}秒
**状态**: {round_info.status.value}
**重试次数**: {round_info.retry_count}

#### 📝 输入提示
```
{round_info.prompt}
```

#### 🤖 AI响应
```
{round_info.response}
```

"""

            if round_info.error_message:
                md_content += f"""#### ❌ 错误信息
```
{round_info.error_message}
```

"""

            if round_info.token_usage:
                md_content += f"""#### 📊 Token使用
- 输入Token: {round_info.token_usage.get('prompt_tokens', 'N/A')}
- 输出Token: {round_info.token_usage.get('completion_tokens', 'N/A')}
- 总Token: {round_info.token_usage.get('total_tokens', 'N/A')}

"""

            md_content += "---\n\n"

        if session.final_result:
            md_content += f"""## 🎯 最终结果
```
{session.final_result}
```

"""

        if session.error_summary:
            md_content += f"""## ❌ 错误摘要
```
{session.error_summary}
```

"""

        if session.metadata:
            md_content += f"""## 📊 元数据
```json
{json.dumps(session.metadata, ensure_ascii=False, indent=2)}
```

"""

        md_content += f"""---
*记录生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*
"""

        return md_content

    def get_session_summary(self, session_id: str) -> Optional[Dict[str, Any]]:
        """获取会话摘要"""
        # 先检查活跃会话
        if session_id in self.active_sessions:
            session = self.active_sessions[session_id]
        else:
            # 从文件加载
            session = self._load_session_from_file(session_id)
            if not session:
                return None

        return {
            "session_id": session.session_id,
            "task_title": session.task_title,
            "task_type": session.task_type,
            "status": session.status.value,
            "rounds_count": len(session.rounds),
            "total_duration": session.total_duration,
            "started_at": session.started_at,
            "ended_at": session.ended_at,
            "has_errors": any(r.error_message for r in session.rounds)
        }

    def _load_session_from_file(self, session_id: str) -> Optional[ConversationSession]:
        """从文件加载会话"""
        try:
            # 搜索所有日期目录
            for date_dir in self.log_dir.iterdir():
                if date_dir.is_dir():
                    json_file = date_dir / f"{session_id}.json"
                    if json_file.exists():
                        with open(json_file, 'r', encoding='utf-8') as f:
                            data = json.load(f)

                        # 转换rounds数据
                        rounds = []
                        for round_data in data.get('rounds', []):
                            if isinstance(round_data, dict):
                                rounds.append(ConversationRound(**round_data))
                            else:
                                rounds.append(round_data)

                        # 创建会话对象
                        session_data = data.copy()
                        session_data['rounds'] = rounds

                        return ConversationSession(**session_data)
            return None
        except Exception as e:
            logger.error(f"Failed to load session {session_id}: {e}")
            return None

    def search_sessions(self, task_type: str = None, status: str = None,
                       date_from: str = None, date_to: str = None) -> List[Dict[str, Any]]:
        """搜索会话"""
        results = []

        try:
            for date_dir in self.log_dir.iterdir():
                if not date_dir.is_dir():
                    continue

                # 日期过滤
                if date_from and date_dir.name < date_from:
                    continue
                if date_to and date_dir.name > date_to:
                    continue

                for json_file in date_dir.glob("*.json"):
                    try:
                        with open(json_file, 'r', encoding='utf-8') as f:
                            data = json.load(f)

                        # 类型过滤
                        if task_type and data.get("task_type") != task_type:
                            continue

                        # 状态过滤
                        if status and data.get("status") != status:
                            continue

                        results.append({
                            "session_id": data.get("session_id"),
                            "task_title": data.get("task_title"),
                            "task_type": data.get("task_type"),
                            "status": data.get("status"),
                            "started_at": data.get("started_at"),
                            "ended_at": data.get("ended_at"),
                            "total_duration": data.get("total_duration"),
                            "rounds_count": len(data.get("rounds", [])),
                            "file_path": str(json_file)
                        })

                    except Exception as e:
                        logger.warning(f"Failed to parse {json_file}: {e}")

            # 按时间排序
            results.sort(key=lambda x: x["started_at"], reverse=True)

        except Exception as e:
            logger.error(f"Failed to search sessions: {e}")

        return results


# 全局对话记录器实例
global_conversation_logger = ConversationLogger()
