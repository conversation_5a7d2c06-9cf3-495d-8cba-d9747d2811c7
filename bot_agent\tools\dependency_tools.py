"""
依赖管理工具 - 智能包管理和依赖分析
"""

import os
import json
import subprocess
import re
from typing import Dict, List, Any, Optional
from pathlib import Path

from .base_tool import BaseTool, ToolResult
from .terminal_tools import TerminalTools


class DependencyTools(BaseTool):
    """
    依赖管理工具

    功能：
    1. 多语言包管理器支持
    2. 依赖安装和更新
    3. 依赖冲突检测
    4. 安全漏洞扫描
    5. 依赖树分析
    """

    def __init__(self):
        super().__init__()
        self.terminal = TerminalTools()
        self.package_managers = {
            'python': {
                'pip': {
                    'install': 'pip install {package}',
                    'uninstall': 'pip uninstall {package} -y',
                    'list': 'pip list',
                    'show': 'pip show {package}',
                    'freeze': 'pip freeze',
                    'check': 'pip check'
                },
                'poetry': {
                    'install': 'poetry add {package}',
                    'uninstall': 'poetry remove {package}',
                    'list': 'poetry show',
                    'show': 'poetry show {package}',
                    'update': 'poetry update'
                },
                'conda': {
                    'install': 'conda install {package} -y',
                    'uninstall': 'conda remove {package} -y',
                    'list': 'conda list',
                    'show': 'conda info {package}'
                }
            },
            'javascript': {
                'npm': {
                    'install': 'npm install {package}',
                    'uninstall': 'npm uninstall {package}',
                    'list': 'npm list',
                    'show': 'npm show {package}',
                    'audit': 'npm audit',
                    'update': 'npm update'
                },
                'yarn': {
                    'install': 'yarn add {package}',
                    'uninstall': 'yarn remove {package}',
                    'list': 'yarn list',
                    'show': 'yarn info {package}',
                    'audit': 'yarn audit'
                },
                'pnpm': {
                    'install': 'pnpm add {package}',
                    'uninstall': 'pnpm remove {package}',
                    'list': 'pnpm list',
                    'audit': 'pnpm audit'
                }
            }
        }

    def get_capabilities(self) -> List[str]:
        """获取工具能力"""
        return [
            'detect_package_manager',
            'install_package',
            'uninstall_package',
            'list_packages',
            'check_dependencies',
            'scan_vulnerabilities',
            'analyze_dependency_tree',
            'update_dependencies'
        ]

    def get_description(self) -> str:
        """获取工具描述"""
        return "依赖管理工具 - 支持Python、JavaScript等多语言包管理"

    async def detect_package_manager(self, project_path: str) -> ToolResult:
        """
        检测项目使用的包管理器

        Args:
            project_path: 项目路径

        Returns:
            ToolResult: 检测结果
        """
        try:
            detected_managers = []

            # 检测Python包管理器
            if os.path.exists(os.path.join(project_path, 'requirements.txt')):
                detected_managers.append({'language': 'python', 'manager': 'pip', 'config_file': 'requirements.txt'})

            if os.path.exists(os.path.join(project_path, 'pyproject.toml')):
                detected_managers.append({'language': 'python', 'manager': 'poetry', 'config_file': 'pyproject.toml'})

            if os.path.exists(os.path.join(project_path, 'environment.yml')):
                detected_managers.append({'language': 'python', 'manager': 'conda', 'config_file': 'environment.yml'})

            # 检测JavaScript包管理器
            if os.path.exists(os.path.join(project_path, 'package.json')):
                if os.path.exists(os.path.join(project_path, 'yarn.lock')):
                    detected_managers.append({'language': 'javascript', 'manager': 'yarn', 'config_file': 'package.json'})
                elif os.path.exists(os.path.join(project_path, 'pnpm-lock.yaml')):
                    detected_managers.append({'language': 'javascript', 'manager': 'pnpm', 'config_file': 'package.json'})
                else:
                    detected_managers.append({'language': 'javascript', 'manager': 'npm', 'config_file': 'package.json'})

            return ToolResult(
                success=True,
                data={
                    'detected_managers': detected_managers,
                    'count': len(detected_managers)
                },
                message=f"检测到 {len(detected_managers)} 个包管理器"
            )

        except Exception as e:
            self.log_error(e, "检测包管理器")
            return ToolResult(
                success=False,
                error=str(e),
                message="包管理器检测失败"
            )

    async def install_package(
        self,
        package_name: str,
        language: str = 'python',
        manager: str = 'pip',
        project_path: str = '.',
        dev_dependency: bool = False
    ) -> ToolResult:
        """
        安装包

        Args:
            package_name: 包名
            language: 语言类型
            manager: 包管理器
            project_path: 项目路径
            dev_dependency: 是否为开发依赖

        Returns:
            ToolResult: 安装结果
        """
        try:
            if language not in self.package_managers:
                return ToolResult(
                    success=False,
                    error=f"不支持的语言: {language}"
                )

            if manager not in self.package_managers[language]:
                return ToolResult(
                    success=False,
                    error=f"不支持的包管理器: {manager}"
                )

            # 构建安装命令
            command_template = self.package_managers[language][manager]['install']
            command = command_template.format(package=package_name)

            # 添加开发依赖标志
            if dev_dependency:
                if manager == 'npm':
                    command += ' --save-dev'
                elif manager == 'yarn':
                    command += ' --dev'
                elif manager == 'poetry':
                    command = command.replace('add', 'add --group dev')

            # 执行安装命令
            result = await self.terminal.execute_command(
                command,
                cwd=project_path,
                timeout=300  # 5分钟超时
            )

            if result.success:
                # 验证安装
                verify_result = await self._verify_package_installation(
                    package_name, language, manager, project_path
                )

                return ToolResult(
                    success=True,
                    data={
                        'package_name': package_name,
                        'language': language,
                        'manager': manager,
                        'dev_dependency': dev_dependency,
                        'install_output': result.data['stdout'],
                        'verified': verify_result
                    },
                    message=f"成功安装包: {package_name}"
                )
            else:
                return ToolResult(
                    success=False,
                    data=result.data,
                    error=result.data.get('stderr', '安装失败'),
                    message=f"安装包失败: {package_name}"
                )

        except Exception as e:
            self.log_error(e, f"安装包: {package_name}")
            return ToolResult(
                success=False,
                error=str(e),
                message=f"安装包异常: {package_name}"
            )

    async def check_dependencies(
        self,
        project_path: str,
        language: str = 'python',
        manager: str = 'pip'
    ) -> ToolResult:
        """
        检查依赖状态

        Args:
            project_path: 项目路径
            language: 语言类型
            manager: 包管理器

        Returns:
            ToolResult: 检查结果
        """
        try:
            check_results = {
                'installed_packages': [],
                'missing_packages': [],
                'outdated_packages': [],
                'conflicts': []
            }

            # 获取已安装包列表
            if language == 'python' and manager == 'pip':
                list_result = await self.terminal.execute_command(
                    'pip list --format=json',
                    cwd=project_path
                )

                if list_result.success:
                    try:
                        installed_packages = json.loads(list_result.data['stdout'])
                        check_results['installed_packages'] = installed_packages
                    except json.JSONDecodeError:
                        pass

                # 检查依赖冲突
                check_result = await self.terminal.execute_command(
                    'pip check',
                    cwd=project_path
                )

                if not check_result.success and check_result.data and isinstance(check_result.data, dict) and check_result.data.get('stdout'):
                    conflicts = check_result.data['stdout'].strip().split('\n')
                    check_results['conflicts'] = [c for c in conflicts if c]

            elif language == 'javascript' and manager == 'npm':
                # 检查npm依赖
                audit_result = await self.terminal.execute_command(
                    'npm audit --json',
                    cwd=project_path
                )

                if audit_result.success:
                    try:
                        audit_data = json.loads(audit_result.data['stdout'])
                        check_results['vulnerabilities'] = audit_data.get('vulnerabilities', {})
                    except json.JSONDecodeError:
                        pass

            return ToolResult(
                success=True,
                data=check_results,
                message=f"依赖检查完成，发现 {len(check_results['conflicts'])} 个冲突"
            )

        except Exception as e:
            self.log_error(e, "检查依赖")
            return ToolResult(
                success=False,
                error=str(e),
                message="依赖检查失败"
            )

    async def scan_vulnerabilities(
        self,
        project_path: str,
        language: str = 'python'
    ) -> ToolResult:
        """
        扫描安全漏洞

        Args:
            project_path: 项目路径
            language: 语言类型

        Returns:
            ToolResult: 扫描结果
        """
        try:
            vulnerabilities = []

            if language == 'python':
                # 使用safety扫描Python包漏洞
                safety_result = await self.terminal.execute_command(
                    'python -m pip install safety && safety check --json',
                    cwd=project_path
                )

                if safety_result.success:
                    try:
                        safety_data = json.loads(safety_result.data['stdout'])
                        vulnerabilities.extend(safety_data)
                    except json.JSONDecodeError:
                        pass

            elif language == 'javascript':
                # 使用npm audit扫描JavaScript包漏洞
                audit_result = await self.terminal.execute_command(
                    'npm audit --json',
                    cwd=project_path
                )

                if audit_result.success:
                    try:
                        audit_data = json.loads(audit_result.data['stdout'])
                        vulnerabilities = audit_data.get('vulnerabilities', {})
                    except json.JSONDecodeError:
                        pass

            severity_counts = {}
            if isinstance(vulnerabilities, list):
                for vuln in vulnerabilities:
                    severity = vuln.get('severity', 'unknown')
                    severity_counts[severity] = severity_counts.get(severity, 0) + 1

            return ToolResult(
                success=True,
                data={
                    'vulnerabilities': vulnerabilities,
                    'total_count': len(vulnerabilities) if isinstance(vulnerabilities, list) else len(vulnerabilities),
                    'severity_counts': severity_counts
                },
                message=f"扫描完成，发现 {len(vulnerabilities) if isinstance(vulnerabilities, list) else len(vulnerabilities)} 个漏洞"
            )

        except Exception as e:
            self.log_error(e, "扫描漏洞")
            return ToolResult(
                success=False,
                error=str(e),
                message="漏洞扫描失败"
            )

    async def _verify_package_installation(
        self,
        package_name: str,
        language: str,
        manager: str,
        project_path: str
    ) -> bool:
        """验证包是否安装成功"""
        try:
            if language == 'python':
                # 尝试导入包
                import_result = await self.terminal.execute_command(
                    f'python -c "import {package_name.split("==")[0].replace("-", "_")}"',
                    cwd=project_path
                )
                return import_result.success

            elif language == 'javascript':
                # 检查node_modules
                node_modules_path = os.path.join(project_path, 'node_modules', package_name)
                return os.path.exists(node_modules_path)

            return False

        except Exception:
            return False

    async def execute(self, *args, **kwargs) -> ToolResult:
        """执行工具操作"""
        # 默认检测包管理器
        if args:
            return await self.detect_package_manager(args[0])
        else:
            return ToolResult(
                success=False,
                error="缺少项目路径参数"
            )
