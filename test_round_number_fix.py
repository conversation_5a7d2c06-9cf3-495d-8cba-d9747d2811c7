#!/usr/bin/env python3
"""
轮次编号修复验证测试
验证AI持续追问是否在同一会话中继续，轮次编号是否连续
"""

import asyncio
import os
import sys
import time
import json
from pathlib import Path

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent))

async def test_round_number_fix():
    """测试轮次编号修复效果"""
    print("🔧 轮次编号修复验证测试")
    print("=" * 50)

    try:
        from bot_agent.engines.task_executor import AiderBasedTaskExecutor
        from bot_agent.tools.intelligent_tool_coordinator import global_tool_coordinator
        from bot_agent.utils.conversation_logger import global_conversation_logger

        # 创建测试任务
        executor = AiderBasedTaskExecutor()

        # 模拟一个会导致AI持续追问的任务
        test_task = {
            "title": "轮次编号测试任务",
            "description": "测试AI持续追问是否在同一会话中继续",
            "task_type": "bug_fix"
        }

        test_project_path = "E:\\aider-git-repos\\ai-proxy"

        print(f"\n🎯 创建测试会话...")

        # 创建会话
        session_id = global_conversation_logger.start_session(
            task_id="round_number_test",
            task_title="轮次编号修复测试",
            task_type="test",
            project_path=test_project_path
        )

        print(f"✅ 会话ID: {session_id}")

        # 记录第1轮
        global_conversation_logger.log_round(
            session_id=session_id,
            round_name="第1轮测试：初始分析",
            prompt="测试提示1",
            response="测试响应1",
            model_name="test-model",
            duration=0.1
        )

        # 记录第2轮
        global_conversation_logger.log_round(
            session_id=session_id,
            round_name="第2轮测试：AI生成修复方案",
            prompt="测试提示2",
            response="测试响应2",
            model_name="test-model",
            duration=0.1
        )

        print(f"\n🔍 测试AI持续追问机制...")

        # 模拟错误，触发AI持续追问
        test_errors = [
            "ValueError: Error code 'Ignore' supplied to 'extend-ignore' option does not match '^[A-Z]{1,3}[0-9]{0,3}$'",
            "flake8配置错误：extend-ignore选项包含无效字符"
        ]

        # 调用AI持续追问机制，传递session_id
        print(f"📞 调用AI持续追问，session_id: {session_id}")

        # 这里应该在同一会话中继续，而不是创建新会话
        result = global_tool_coordinator.ai_continuous_inquiry_fix(
            errors=test_errors,
            project_path=test_project_path,
            session_id=session_id  # 关键：传递session_id
        )

        print(f"\n✅ AI持续追问完成")
        print(f"- 成功: {result.success}")
        print(f"- 消息: {result.message}")

        # 结束会话
        global_conversation_logger.end_session(session_id, "测试完成", "success")

        # 检查日志文件
        print(f"\n📋 检查日志文件...")

        # 查找今天的日志文件
        today = time.strftime("%Y-%m-%d")
        log_dir = Path(f"logs/conversations/{today}")

        if log_dir.exists():
            # 查找测试会话的日志文件
            test_log_files = list(log_dir.glob("round_number_test_*.json"))
            ai_inquiry_files = list(log_dir.glob("ai_inquiry_*.json"))

            print(f"📁 找到测试日志文件: {len(test_log_files)} 个")
            print(f"📁 找到AI追问日志文件: {len(ai_inquiry_files)} 个")

            if test_log_files:
                # 读取最新的测试日志文件
                latest_log = max(test_log_files, key=lambda x: x.stat().st_mtime)

                with open(latest_log, 'r', encoding='utf-8') as f:
                    log_data = json.load(f)

                print(f"\n📊 日志分析结果:")
                print(f"- 会话ID: {log_data.get('session_id')}")
                print(f"- 总轮次: {len(log_data.get('rounds', []))}")

                # 检查轮次编号是否连续
                rounds = log_data.get('rounds', [])
                round_numbers = [r.get('round_number') for r in rounds]

                print(f"- 轮次编号: {round_numbers}")

                # 验证轮次编号连续性
                if round_numbers == list(range(1, len(round_numbers) + 1)):
                    print("✅ 轮次编号连续正确")
                else:
                    print("❌ 轮次编号不连续")

                # 检查轮次名称
                round_names = [r.get('round_name') for r in rounds]
                print(f"- 轮次名称: {round_names}")

            # 检查是否有单独的AI追问文件（应该没有）
            recent_ai_files = [f for f in ai_inquiry_files
                             if f.stat().st_mtime > time.time() - 300]  # 最近5分钟

            if recent_ai_files:
                print(f"❌ 发现 {len(recent_ai_files)} 个新的AI追问文件，说明AI追问被分离了")
                for f in recent_ai_files:
                    print(f"  - {f.name}")
            else:
                print("✅ 没有发现新的AI追问文件，AI追问在同一会话中")

        print(f"\n🎉 测试完成")
        return True

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_session_id_passing():
    """测试session_id传递是否正确"""
    print(f"\n🔍 测试session_id传递...")

    try:
        from bot_agent.tools.intelligent_tool_coordinator import global_tool_coordinator
        import inspect

        coordinator = global_tool_coordinator

        # 检查方法签名
        methods_to_check = [
            'execute_targeted_fixes',
            'ai_continuous_inquiry_fix'  # 同步版本的方法名
        ]

        for method_name in methods_to_check:
            if hasattr(coordinator, method_name):
                method = getattr(coordinator, method_name)
                sig = inspect.signature(method)
                params = list(sig.parameters.keys())

                if 'session_id' in params:
                    print(f"✅ {method_name} 方法有session_id参数")
                else:
                    print(f"❌ {method_name} 方法缺少session_id参数")
            else:
                print(f"❌ {method_name} 方法不存在")

        return True

    except Exception as e:
        print(f"❌ session_id传递测试失败: {e}")
        return False

async def main():
    """主测试函数"""
    print("🚀 开始轮次编号修复验证")

    # 测试1：session_id传递
    test1_result = await test_session_id_passing()

    # 测试2：实际轮次编号
    test2_result = await test_round_number_fix()

    print(f"\n📊 测试结果总结:")
    print(f"- session_id传递测试: {'✅ 通过' if test1_result else '❌ 失败'}")
    print(f"- 轮次编号测试: {'✅ 通过' if test2_result else '❌ 失败'}")

    if test1_result and test2_result:
        print(f"\n🎉 所有测试通过！轮次编号修复成功！")
        print(f"- AI持续追问将在同一会话中继续")
        print(f"- 轮次编号将连续递增")
        print(f"- 不再创建单独的ai_inquiry_xxx任务")
    else:
        print(f"\n❌ 部分测试失败，需要进一步修复")

if __name__ == "__main__":
    asyncio.run(main())
