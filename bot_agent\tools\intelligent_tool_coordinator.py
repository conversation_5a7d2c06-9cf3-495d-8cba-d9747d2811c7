"""
智能工具协调器
为AI提供智能化的工具选择和协同能力，实现真正的工具协同作业
"""

import logging
import asyncio
import threading
import os
import glob
from typing import Dict, Any, List, Optional, Union
from dataclasses import dataclass

from bot_agent.clients.gitlab_client import GitLabClient
from bot_agent.tools.terminal_tools import TerminalTools
from bot_agent.tools.log_analysis_tools import LogAnalysisTools
from bot_agent.tools.testing_tools import TestingTools
from bot_agent.utils.logging_config import get_logger

logger = get_logger(__name__)


# 使用base_tool中的ToolResult
from bot_agent.tools.base_tool import ToolResult


@dataclass
class AnalysisContext:
    """分析上下文"""
    job_id: int
    project_id: int
    project_path: str
    job_info: Dict[str, Any] = None
    job_log: str = None
    error_analysis: Dict[str, Any] = None
    fix_plan: Dict[str, Any] = None


class IntelligentToolCoordinator:
    """
    智能工具协调器

    为AI提供智能化的工具选择和协同能力：
    1. 根据任务类型智能选择工具组合
    2. 协调多个工具完成复杂任务
    3. 提供统一的工具接口
    4. 监控工具执行状态和结果
    """

    def __init__(self):
        """初始化协调器"""
        self.gitlab_client = GitLabClient()
        self.terminal = TerminalTools()
        self.log_analyzer = LogAnalysisTools()
        self.testing = TestingTools()

        # 工具注册表
        self.tools = {
            'gitlab': self.gitlab_client,
            'terminal': self.terminal,
            'log_analyzer': self.log_analyzer,
            'testing': self.testing
        }

        logger.info("IntelligentToolCoordinator initialized")

    async def get_job_info_and_log(self, job_id: int, project_id: int) -> ToolResult:
        """获取作业信息和日志"""
        try:
            logger.info(f"🔍 获取Job {job_id}的信息和日志...")

            # 并行获取作业信息和日志
            job_info_task = asyncio.create_task(self._get_job_info(job_id, project_id))
            job_log_task = asyncio.create_task(self._get_job_log(job_id, project_id))

            job_info, job_log = await asyncio.gather(job_info_task, job_log_task)

            if not job_info:
                return ToolResult(
                    success=False,
                    message="无法获取作业信息",
                    data=None
                )

            if not job_log:
                return ToolResult(
                    success=False,
                    message="无法获取作业日志",
                    data=None
                )

            return ToolResult(
                success=True,
                message=f"成功获取Job {job_id}的信息和日志（{len(job_log)}字符）",
                data={
                    'job_info': job_info,
                    'job_log': job_log,
                    'log_length': len(job_log),
                    'job_status': job_info.get('status', 'unknown'),
                    'failure_reason': job_info.get('failure_reason', 'unknown')
                }
            )

        except Exception as e:
            logger.error(f"获取作业信息和日志失败: {e}")
            return ToolResult(
                success=False,
                message=f"获取失败: {str(e)}",
                data=None
            )

    async def analyze_job_errors(self, job_log: str, job_info: Dict[str, Any]) -> ToolResult:
        """智能分析作业错误"""
        try:
            logger.info("🔍 开始智能错误分析...")

            # 使用日志分析工具
            analysis_result = await self.log_analyzer.analyze_job_log(job_log)

            # 根据作业类型进行特定分析
            job_name = job_info.get('name', '').lower()
            specific_analysis = await self._analyze_by_job_type(job_log, job_name)

            # 合并分析结果
            combined_analysis = {
                'general_analysis': analysis_result,
                'specific_analysis': specific_analysis,
                'job_type': job_name,
                'all_errors': [],
                'error_categories': {}
            }

            # 提取所有错误
            if analysis_result and 'errors' in analysis_result:
                combined_analysis['all_errors'].extend(analysis_result['errors'])

            if specific_analysis and 'errors' in specific_analysis:
                combined_analysis['all_errors'].extend(specific_analysis['errors'])

            # 去重（处理字典类型的错误）
            seen_errors = set()
            unique_errors = []
            for error in combined_analysis['all_errors']:
                if isinstance(error, dict):
                    # 对于字典类型，使用内容作为唯一标识
                    error_key = error.get('content', str(error))
                else:
                    error_key = str(error)

                if error_key not in seen_errors:
                    seen_errors.add(error_key)
                    unique_errors.append(error)

            combined_analysis['all_errors'] = unique_errors

            # 错误分类
            combined_analysis['error_categories'] = self._categorize_errors(combined_analysis['all_errors'])

            return ToolResult(
                success=True,
                message=f"发现 {len(combined_analysis['all_errors'])} 个错误",
                data=combined_analysis
            )

        except Exception as e:
            logger.error(f"错误分析失败: {e}")
            return ToolResult(
                success=False,
                message=f"分析失败: {str(e)}",
                data=None
            )

    async def execute_targeted_fixes(self, error_analysis: Dict[str, Any], project_path: str) -> ToolResult:
        """执行AI驱动的智能修复"""
        try:
            logger.info("🤖 开始AI驱动的智能修复...")

            # 获取所有错误
            all_errors = error_analysis.get('all_errors', [])
            error_categories = error_analysis.get('error_categories', {})

            if not all_errors:
                return ToolResult(
                    success=False,
                    message="没有发现需要修复的错误",
                    data={}
                )

            # 尝试使用AI生成修复方案
            try:
                ai_fix_result = await self._ai_generate_fix_plan(all_errors, project_path)

                if ai_fix_result.success:
                    # 执行AI生成的修复方案
                    fix_plan = ai_fix_result.data.get('fix_plan', [])
                    fix_results = []

                    for i, fix_step in enumerate(fix_plan):
                        logger.info(f"🔧 执行修复步骤 {i+1}/{len(fix_plan)}: {fix_step.get('description', '')}")

                        step_result = await self._execute_ai_fix_step(fix_step, project_path)
                        fix_results.append(step_result)

                        # 如果关键步骤失败，可以选择继续或停止
                        step_success = self._extract_success_status(step_result)
                        if not step_success and fix_step.get('critical', False):
                            logger.warning(f"关键修复步骤失败: {fix_step.get('description', '')}")

                    # 计算总体修复成功率
                    total_fixes = len(fix_results)
                    successful_fixes = sum(1 for r in fix_results if self._extract_success_status(r))

                    return ToolResult(
                        success=successful_fixes > 0,
                        message=f"AI修复执行了 {total_fixes} 个步骤，成功 {successful_fixes} 个",
                        data={
                            'fix_results': fix_results,
                            'total_fixes': total_fixes,
                            'successful_fixes': successful_fixes,
                            'success_rate': successful_fixes / total_fixes if total_fixes > 0 else 0,
                            'ai_generated': True,
                            'fix_plan': fix_plan
                        }
                    )
                else:
                    logger.warning("AI修复方案生成失败，回退到策略修复")

            except Exception as e:
                logger.warning(f"AI修复过程异常: {e}，回退到策略修复")

            # 回退到策略修复
            return await self._fallback_strategy_fixes(error_categories, project_path)

        except Exception as e:
            logger.error(f"执行修复失败: {e}")
            return ToolResult(
                success=False,
                message=f"修复失败: {str(e)}",
                data={}
            )

    async def _ai_generate_fix_plan(self, errors: List[str], project_path: str) -> ToolResult:
        """使用AI生成修复方案（增强版：包含项目上下文）"""
        try:
            logger.info("🤖 AI正在分析错误并生成修复方案...")

            # 收集项目上下文信息
            from bot_agent.tools.project_context_collector import project_context_collector

            logger.info("📋 收集项目上下文信息...")
            project_context = await project_context_collector.collect_full_context(
                project_path,
                error_info={'errors': errors}
            )

            # 格式化项目上下文
            context_text = project_context_collector.format_context_for_ai(
                project_context,
                {'errors': errors}
            )

            # 构建AI提示词
            error_summary = self._format_errors_for_ai(errors)

            ai_prompt = f"""
你是一个专业的配置文件修复专家和DevOps工程师。请基于项目上下文分析错误并生成精准的修复方案。

{context_text}

## 🚨 错误信息
{error_summary}

## 🎯 专业修复策略

### 🔍 配置文件错误专门处理：
1. **flake8配置错误**:
   - 重点关注 `.flake8` 文件中的 `extend-ignore` 选项
   - 无效字符（如#号）需要被移除，保留有效的错误代码格式（如E501, W503）
   - 修复后验证配置语法正确性

2. **requirements.txt错误**:
   - 识别无效的依赖声明格式（如[dev]这种方括号格式）
   - 注释或删除无效行，保持有效的依赖声明

3. **INI/配置文件语法错误**:
   - 检查配置文件的语法结构
   - 修复格式问题，确保配置解析器能正确读取

### 🛠️ 修复工具和方法：
- **文件操作**: 使用标准的文件读写操作
- **配置解析**: 理解各种配置文件格式的语法规则
- **验证命令**: 修复后使用相应工具验证配置正确性
- **备份策略**: 重要修改前先备份

### 📋 分析要求
1. **精准识别错误类型**：配置文件错误 vs 代码错误 vs 依赖错误
2. **结合项目上下文**分析每个错误的根本原因
3. **生成针对性修复方案**：不使用通用命令，而是针对具体问题的精确修复
4. **考虑操作系统兼容性**（当前环境：{project_context.get('environment_info', {}).get('data', {}).get('system', 'unknown')}）
5. **按优先级排序**修复步骤，配置文件错误优先级最高
6. **标明关键步骤**，失败时需要替代方案

### 💡 修复原则：
- **最小修改**: 只修改有问题的部分，保持其他配置不变
- **语法正确**: 确保修复后的配置文件语法完全正确
- **功能保持**: 修复不应影响原有的功能配置
- **可验证**: 每个修复步骤都应该有验证方法

## 🎯 输出格式
请返回JSON格式的修复计划：
{{
    "analysis": "基于项目上下文的错误分析总结，重点说明配置文件问题",
    "project_insights": "项目特点和配置文件相关注意事项",
    "config_file_focus": "重点关注的配置文件和修复策略",
    "fix_plan": [
        {{
            "step": 1,
            "description": "详细的修复步骤描述，说明具体要修改什么",
            "action_type": "file_read|file_write|command|validation",
            "target_file": "目标配置文件路径（如果适用）",
            "command": "具体的命令或操作（适配当前操作系统）",
            "operation_details": "具体的操作细节，如要修改的行、要移除的字符等",
            "critical": true/false,
            "expected_result": "预期结果",
            "verification_method": "如何验证修复效果",
            "context_reason": "基于项目上下文的选择理由",
            "rollback_info": "如果需要回滚的信息"
        }}
    ],
    "estimated_duration": 预估修复时间（秒）,
    "risk_level": "low|medium|high",
    "success_probability": 0.0-1.0
}}

## 📝 特别提醒：
- 如果是flake8配置错误，重点检查extend-ignore选项中的无效字符
- 如果是requirements.txt错误，重点检查无效的依赖声明格式
- 优先使用文件操作而不是通用的检查命令
- 每个修复步骤都要有明确的验证方法

现在开始分析并生成修复方案：
"""

            # 调用AI生成修复方案
            from bot_agent.utils.llm_retry_handler import enhanced_llm_call
            import httpx
            import json

            async def call_openrouter_api():
                """调用OpenRouter API"""
                api_key = os.getenv("OPENROUTER_API_KEY")
                if not api_key:
                    raise Exception("OPENROUTER_API_KEY环境变量未设置")

                headers = {
                    "Authorization": f"Bearer {api_key}",
                    "Content-Type": "application/json",
                    "HTTP-Referer": "https://github.com/aider-plus/aider-plus",
                    "X-Title": "Aider Plus AI Fix Generator"
                }

                data = {
                    "model": "deepseek/deepseek-r1:free",
                    "messages": [{"role": "user", "content": ai_prompt}],
                    "temperature": 0.1,
                    "max_tokens": 2000
                }

                async with httpx.AsyncClient(timeout=60.0) as client:
                    response = await client.post(
                        "https://openrouter.ai/api/v1/chat/completions",
                        headers=headers,
                        json=data
                    )
                    response.raise_for_status()
                    result = response.json()

                    if "choices" in result and len(result["choices"]) > 0:
                        return {"content": result["choices"][0]["message"]["content"]}
                    else:
                        return None

            # 使用增强的LLM调用
            response = await enhanced_llm_call(
                call_openrouter_api,
                task_info={"title": "AI修复方案生成", "task_type": "fix_generation"}
            )

            if not response or not response.get('content'):
                return ToolResult(
                    success=False,
                    message="AI修复方案生成失败",
                    data={}
                )

            # 解析AI响应
            import json
            try:
                ai_response = response['content']
                # 尝试提取JSON部分
                if '```json' in ai_response:
                    json_start = ai_response.find('```json') + 7
                    json_end = ai_response.find('```', json_start)
                    json_content = ai_response[json_start:json_end].strip()
                elif '{' in ai_response and '}' in ai_response:
                    json_start = ai_response.find('{')
                    json_end = ai_response.rfind('}') + 1
                    json_content = ai_response[json_start:json_end]
                else:
                    json_content = ai_response

                fix_plan_data = json.loads(json_content)

                return ToolResult(
                    success=True,
                    message="AI修复方案生成成功",
                    data=fix_plan_data
                )

            except json.JSONDecodeError as e:
                logger.warning(f"AI响应JSON解析失败: {e}")
                # 回退到文本解析
                return self._parse_ai_text_response(ai_response)

        except Exception as e:
            logger.error(f"AI修复方案生成异常: {e}")
            return ToolResult(
                success=False,
                message=f"AI修复方案生成异常: {str(e)}",
                data={}
            )

    def _format_errors_for_ai(self, errors: List[str]) -> str:
        """格式化错误信息供AI分析"""
        formatted_errors = []

        for i, error in enumerate(errors, 1):
            if isinstance(error, dict):
                error_content = error.get('content', str(error))
                error_type = error.get('type', 'unknown')
                formatted_errors.append(f"{i}. [{error_type}] {error_content}")
            else:
                formatted_errors.append(f"{i}. {str(error)}")

        return "\n".join(formatted_errors)

    def _parse_ai_text_response(self, response: str) -> ToolResult:
        """解析AI的文本响应，提取修复步骤"""
        try:
            # 简单的文本解析逻辑
            lines = response.split('\n')
            fix_steps = []

            current_step = {}
            step_counter = 1

            for line in lines:
                line = line.strip()
                if not line:
                    continue

                # 检测命令行
                if line.startswith('$') or line.startswith('pip ') or line.startswith('black ') or line.startswith('flake8'):
                    if current_step:
                        fix_steps.append(current_step)

                    current_step = {
                        "step": step_counter,
                        "description": f"执行命令: {line}",
                        "command": line.lstrip('$ '),
                        "critical": False,
                        "expected_result": "命令执行成功"
                    }
                    step_counter += 1

            if current_step:
                fix_steps.append(current_step)

            return ToolResult(
                success=len(fix_steps) > 0,
                message=f"从AI响应中解析出 {len(fix_steps)} 个修复步骤",
                data={
                    "analysis": "AI文本响应解析",
                    "fix_plan": fix_steps
                }
            )

        except Exception as e:
            logger.error(f"AI文本响应解析失败: {e}")
            return ToolResult(
                success=False,
                message=f"AI响应解析失败: {str(e)}",
                data={}
            )

    async def _execute_ai_fix_step(self, fix_step: Dict[str, Any], project_path: str) -> Dict[str, Any]:
        """执行AI生成的修复步骤（支持多轮重试和智能替代）"""
        try:
            command = fix_step.get('command', '')
            description = fix_step.get('description', '')

            logger.info(f"🔧 执行AI修复步骤: {description}")
            logger.info(f"📝 执行命令: {command}")

            if not command:
                return {
                    'success': False,
                    'message': '修复步骤缺少命令',
                    'description': description
                }

            # 支持多轮重试的执行
            return await self._execute_command_with_retry(command, description, project_path)

        except Exception as e:
            logger.error(f"AI修复步骤执行异常: {e}")
            return {
                'success': False,
                'message': f'步骤执行异常: {str(e)}',
                'description': fix_step.get('description', ''),
                'command': fix_step.get('command', '')
            }

    async def _execute_command_with_retry(self, command: str, description: str, project_path: str, max_retries: int = 3) -> Dict[str, Any]:
        """执行命令，支持多轮重试和AI智能替代"""
        attempts = []
        current_command = command

        for attempt in range(1, max_retries + 1):
            logger.info(f"🔄 第 {attempt} 次尝试执行: {current_command}")

            try:
                # 执行命令
                result = await self.terminal.execute_command(current_command, cwd=project_path)

                attempt_info = {
                    'attempt': attempt,
                    'command': current_command,
                    'success': result.success if result else False,
                    'output': result.data.get('stdout', '') if result and result.data else '',
                    'error': result.data.get('stderr', '') if result and result.data else ''
                }
                attempts.append(attempt_info)

                if result and result.success:
                    logger.info(f"✅ 第 {attempt} 次尝试成功")
                    return {
                        'success': True,
                        'message': f"步骤执行成功: {description}",
                        'description': description,
                        'command': current_command,
                        'output': result.data,
                        'attempts': attempts,
                        'final_attempt': attempt
                    }
                else:
                    error_msg = result.data.get('stderr', '') if result and result.data else '命令执行失败'
                    logger.warning(f"❌ 第 {attempt} 次尝试失败: {error_msg}")

                    # 如果不是最后一次尝试，使用AI生成替代命令
                    if attempt < max_retries:
                        alternative_command = await self._get_ai_alternative_command(
                            original_command=current_command,
                            error_message=error_msg,
                            description=description,
                            project_path=project_path
                        )

                        if alternative_command and alternative_command != current_command:
                            logger.info(f"🤖 AI建议替代命令: {alternative_command}")
                            current_command = alternative_command
                        else:
                            logger.warning(f"⚠️ AI无法提供替代命令，继续使用原命令")

            except Exception as e:
                logger.error(f"❌ 第 {attempt} 次尝试异常: {e}")
                attempts.append({
                    'attempt': attempt,
                    'command': current_command,
                    'success': False,
                    'error': str(e)
                })

        # 所有尝试都失败
        return {
            'success': False,
            'message': f'步骤执行失败（{max_retries}次尝试）: {description}',
            'description': description,
            'command': command,
            'attempts': attempts,
            'final_attempt': max_retries
        }

    async def _get_ai_alternative_command(self, original_command: str, error_message: str, description: str, project_path: str) -> Optional[str]:
        """生成替代命令（使用预定义映射 + AI备用）"""
        try:
            logger.info("🔄 生成替代命令...")

            # 首先尝试预定义的命令映射
            alternative = self._get_predefined_alternative(original_command, error_message)
            if alternative:
                logger.info(f"📋 使用预定义替代命令: {alternative}")
                return alternative

            # 如果预定义映射没有找到，尝试AI生成（如果API可用）
            ai_alternative = await self._try_ai_alternative(original_command, error_message, description, project_path)
            if ai_alternative:
                logger.info(f"🤖 AI生成替代命令: {ai_alternative}")
                return ai_alternative

            logger.warning("⚠️ 无法生成替代命令")
            return None

        except Exception as e:
            logger.error(f"替代命令生成失败: {e}")
            return None

    def _get_predefined_alternative(self, original_command: str, error_message: str) -> Optional[str]:
        """获取预定义的命令替代方案"""
        import platform

        # 检测操作系统
        is_windows = platform.system() == "Windows"

        if not is_windows:
            return None  # 非Windows环境不需要替代

        # Windows环境下的命令映射
        command_mappings = {
            # grep 命令映射 - 使用Get-ChildItem递归查找文件，然后用Select-String搜索
            r'grep\s+-r\s+[\'"]([^\'\"]+)[\'"]\s+\.\/\*': r'Get-ChildItem -Path . -Recurse -File | Select-String -Pattern "\1"',
            r'grep\s+-r\s+[\'"]([^\'\"]+)[\'"]\s+\.\*': r'Get-ChildItem -Path . -Recurse -File | Select-String -Pattern "\1"',
            r'grep\s+-r\s+([^\s]+)\s+\.\/\*': r'Get-ChildItem -Path . -Recurse -File | Select-String -Pattern "\1"',
            r'grep\s+-r\s+([^\s]+)\s+\.\*': r'Get-ChildItem -Path . -Recurse -File | Select-String -Pattern "\1"',

            # find 命令映射
            r'find\s+\.\s+-name\s+[\'"]([^\'\"]+)[\'"]': r'Get-ChildItem -Path . -Name "\1" -Recurse',
            r'find\s+\.\s+-name\s+([^\s]+)': r'Get-ChildItem -Path . -Name "\1" -Recurse',

            # cat 命令映射
            r'cat\s+([^\s]+)': r'Get-Content "\1"',

            # ls 命令映射
            r'ls\s+-la?': r'Get-ChildItem -Force',
            r'ls\s+([^\s]+)': r'Get-ChildItem "\1"',
            r'^ls$': r'Get-ChildItem',

            # rm 命令映射
            r'rm\s+-rf?\s+([^\s]+)': r'Remove-Item "\1" -Recurse -Force',
            r'rm\s+([^\s]+)': r'Remove-Item "\1"',

            # 其他常用命令
            r'which\s+([^\s]+)': r'Get-Command "\1"',
            r'wc\s+-l\s+([^\s]+)': r'(Get-Content "\1" | Measure-Object -Line).Lines',
            r'head\s+-n?\s*(\d+)\s+([^\s]+)': r'Get-Content "\2" -TotalCount \1',
            r'tail\s+-n?\s*(\d+)\s+([^\s]+)': r'Get-Content "\2" -Tail \1',
        }

        # 检查是否是命令不存在的错误
        if "无法将" in error_message and "项识别为 cmdlet" in error_message:
            import re

            for pattern, replacement in command_mappings.items():
                match = re.match(pattern, original_command.strip())
                if match:
                    # 使用正则表达式替换
                    alternative = re.sub(pattern, replacement, original_command.strip())
                    return alternative

        return None

    async def _try_ai_alternative(self, original_command: str, error_message: str, description: str, project_path: str) -> Optional[str]:
        """尝试使用AI生成替代命令（如果API可用）"""
        try:
            # 检查API密钥是否可用
            import os
            api_key = os.getenv("OPENROUTER_API_KEY")
            if not api_key:
                logger.debug("OPENROUTER_API_KEY未设置，跳过AI生成")
                return None

            # 检测操作系统和环境
            import platform
            os_type = platform.system()

            ai_prompt = f"""
你是一个专业的DevOps工程师。原始命令执行失败，请提供一个替代命令。

## 环境信息
- 操作系统: {os_type}
- 项目路径: {project_path}
- Shell: {"PowerShell" if os_type == "Windows" else "bash"}

## 失败的命令
```
{original_command}
```

## 错误信息
```
{error_message}
```

## 任务描述
{description}

## 要求
1. 分析错误原因（如命令不存在、语法错误、权限问题等）
2. 提供一个在当前环境下可以工作的替代命令
3. 确保替代命令能达到相同的目标
4. 考虑跨平台兼容性

## 常见替代方案
- Linux `grep` → Windows `Select-String`
- Linux `find` → Windows `Get-ChildItem`
- Linux `cat` → Windows `Get-Content`
- Linux `ls` → Windows `dir` 或 `Get-ChildItem`
- Linux `rm` → Windows `Remove-Item`

## 输出格式
只返回替代命令，不要包含解释或其他文本。如果无法提供替代命令，返回"NO_ALTERNATIVE"。

替代命令:
"""

            # 调用AI生成替代命令
            from bot_agent.utils.llm_retry_handler import enhanced_llm_call
            import httpx

            async def call_openrouter_api():
                """调用OpenRouter API"""
                headers = {
                    "Authorization": f"Bearer {api_key}",
                    "Content-Type": "application/json",
                    "HTTP-Referer": "https://github.com/aider-plus/aider-plus",
                    "X-Title": "Aider Plus Command Alternative Generator"
                }

                data = {
                    "model": "deepseek/deepseek-r1:free",
                    "messages": [{"role": "user", "content": ai_prompt}],
                    "temperature": 0.1,
                    "max_tokens": 200
                }

                async with httpx.AsyncClient(timeout=30.0) as client:
                    response = await client.post(
                        "https://openrouter.ai/api/v1/chat/completions",
                        headers=headers,
                        json=data
                    )
                    response.raise_for_status()
                    result = response.json()

                    if "choices" in result and len(result["choices"]) > 0:
                        return {"content": result["choices"][0]["message"]["content"]}
                    else:
                        return None

            # 使用增强的LLM调用
            response = await enhanced_llm_call(
                call_openrouter_api,
                task_info={"title": "命令替代生成", "task_type": "command_alternative"}
            )

            if response and response.get('content'):
                alternative = response['content'].strip()

                # 清理AI响应
                if alternative.startswith('替代命令:'):
                    alternative = alternative[5:].strip()

                # 移除代码块标记
                if alternative.startswith('```'):
                    lines = alternative.split('\n')
                    alternative = '\n'.join(lines[1:-1]) if len(lines) > 2 else alternative

                alternative = alternative.strip()

                if alternative and alternative != "NO_ALTERNATIVE" and alternative != original_command:
                    return alternative
                else:
                    return None
            else:
                return None

        except Exception as e:
            logger.debug(f"AI替代命令生成失败: {e}")
            return None

    async def _fallback_strategy_fixes(self, error_categories: Dict[str, List], project_path: str) -> ToolResult:
        """回退到策略修复"""
        try:
            logger.info("🔄 回退到策略修复模式...")

            fix_results = []

            # 根据错误类型执行不同的修复策略
            for category, errors in error_categories.items():
                logger.info(f"🔧 处理 {category} 类型错误: {len(errors)} 个")

                if category == 'syntax_errors':
                    result = await self._fix_syntax_errors(errors, project_path)
                elif category == 'dependency_errors':
                    result = await self._fix_dependency_errors(errors, project_path)
                elif category == 'test_failures':
                    result = await self._fix_test_failures(errors, project_path)
                elif category == 'lint_errors':
                    result = await self._fix_lint_errors(errors, project_path)
                elif category == 'build_errors':
                    result = await self._fix_build_errors(errors, project_path)
                else:
                    result = await self._fix_generic_errors(errors, project_path)

                fix_results.append(result)
                logger.info(f"修复结果: {result}")

            # 汇总修复结果
            total_fixes = len(fix_results)
            successful_fixes = sum(1 for r in fix_results if r.get('success', False))

            return ToolResult(
                success=successful_fixes > 0,
                message=f"策略修复执行了 {total_fixes} 个修复，成功 {successful_fixes} 个",
                data={
                    'fix_results': fix_results,
                    'total_fixes': total_fixes,
                    'successful_fixes': successful_fixes,
                    'success_rate': successful_fixes / total_fixes if total_fixes > 0 else 0,
                    'ai_generated': False,
                    'fallback_used': True
                }
            )

        except Exception as e:
            logger.error(f"策略修复失败: {e}")
            return ToolResult(
                success=False,
                message=f"策略修复失败: {str(e)}",
                data={}
            )

    async def verify_fixes(self, project_path: str, job_type: str) -> ToolResult:
        """验证修复效果"""
        try:
            logger.info("✅ 开始验证修复效果...")

            verification_results = []

            # 根据作业类型选择验证策略
            if 'test' in job_type:
                result = await self.testing.run_tests(project_path)
                verification_results.append({
                    'type': 'test_execution',
                    'success': result.success if result else False,
                    'message': result.data if result else 'Test execution failed'
                })

            if 'lint' in job_type:
                # 执行Black格式检查
                black_result = await self.terminal.execute_command(
                    'black --check --config pyproject.toml .',
                    cwd=project_path
                )

                # 执行flake8检查
                flake8_result = await self.terminal.execute_command(
                    'flake8 .',
                    cwd=project_path
                )

                # 综合评估lint检查结果
                lint_success = (black_result and black_result.success) and (flake8_result and flake8_result.success)

                lint_messages = []
                if black_result and not black_result.success:
                    lint_messages.append("Black格式检查失败")
                if flake8_result and not flake8_result.success:
                    lint_messages.append("Flake8检查失败")

                verification_results.append({
                    'type': 'lint_check',
                    'success': lint_success,
                    'message': '; '.join(lint_messages) if lint_messages else 'Lint检查通过',
                    'details': {
                        'black_success': black_result.success if black_result else False,
                        'flake8_success': flake8_result.success if flake8_result else False,
                        'black_output': black_result.data.get('stdout', '') if black_result and black_result.data else '',
                        'flake8_output': flake8_result.data.get('stdout', '') if flake8_result and flake8_result.data else ''
                    }
                })

            # 基本语法检查 - 修复Windows通配符问题
            try:
                import os
                import glob

                # 查找所有Python文件
                python_files = []
                for root, dirs, files in os.walk(project_path):
                    # 跳过常见的忽略目录
                    dirs[:] = [d for d in dirs if not d.startswith('.') and d not in ['__pycache__', 'node_modules', 'venv', 'env']]
                    for file in files:
                        if file.endswith('.py'):
                            python_files.append(os.path.relpath(os.path.join(root, file), project_path))

                if python_files:
                    # 限制检查文件数量，避免命令过长
                    files_to_check = python_files[:10]  # 最多检查10个文件
                    files_str = ' '.join(f'"{f}"' for f in files_to_check)
                    command = f'python -m py_compile {files_str}'
                    result = await self.terminal.execute_command(command, cwd=project_path)

                    verification_results.append({
                        'type': 'syntax_check',
                        'success': result.success if result else False,
                        'message': f'检查了 {len(files_to_check)} 个Python文件' if result and result.success else 'Syntax check failed',
                        'details': f'检查文件: {", ".join(files_to_check[:3])}{"..." if len(files_to_check) > 3 else ""}'
                    })
                else:
                    verification_results.append({
                        'type': 'syntax_check',
                        'success': True,
                        'message': '未找到Python文件，跳过语法检查'
                    })

            except Exception as e:
                logger.error(f"语法检查失败: {e}")
                verification_results.append({
                    'type': 'syntax_check',
                    'success': False,
                    'message': f'语法检查异常: {str(e)}'
                })

            # 汇总验证结果
            total_verifications = len(verification_results)
            successful_verifications = sum(1 for r in verification_results if r.get('success', False))

            return ToolResult(
                success=successful_verifications > 0,
                message=f"验证了 {total_verifications} 项，成功 {successful_verifications} 项",
                data={
                    'verification_results': verification_results,
                    'total_verifications': total_verifications,
                    'successful_verifications': successful_verifications,
                    'success_rate': successful_verifications / total_verifications if total_verifications > 0 else 0
                }
            )

        except Exception as e:
            logger.error(f"验证失败: {e}")
            return ToolResult(
                success=False,
                message=f"验证失败: {str(e)}",
                data={}
            )

    # 私有辅助方法
    async def _get_job_info(self, job_id: int, project_id: int) -> Optional[Dict[str, Any]]:
        """获取作业信息"""
        try:
            return self.gitlab_client.get_job(project_id, job_id)
        except Exception as e:
            logger.error(f"获取作业信息失败: {e}")
            return None

    async def _get_job_log(self, job_id: int, project_id: int) -> Optional[str]:
        """获取作业日志"""
        try:
            return self.gitlab_client.get_job_log(project_id, job_id)
        except Exception as e:
            logger.error(f"获取作业日志失败: {e}")
            return None

    async def _analyze_by_job_type(self, job_log: str, job_type: str) -> Dict[str, Any]:
        """根据作业类型进行特定分析"""
        errors = []

        if job_type == 'lint':
            errors.extend(self._extract_lint_errors(job_log))
        elif job_type == 'test':
            errors.extend(self._extract_test_errors(job_log))
        elif job_type == 'build':
            errors.extend(self._extract_build_errors(job_log))
        else:
            # 通用错误提取
            errors.extend(self._extract_generic_errors(job_log))

        return {
            'errors': errors,
            'analysis_type': job_type,
            'error_count': len(errors)
        }

    def _extract_lint_errors(self, job_log: str) -> List[str]:
        """提取lint错误"""
        errors = []
        lines = job_log.split('\n')

        for line in lines:
            line = line.strip()
            if not line:
                continue

            # flake8错误模式
            if '.py:' in line and any(code in line for code in ['E', 'W', 'F']):
                errors.append(line)

            # black错误模式
            elif 'would reformat' in line.lower():
                errors.append(line)

            # pylint错误模式
            elif any(pattern in line.lower() for pattern in ['pylint', 'convention', 'refactor', 'warning', 'error']):
                if '.py:' in line:
                    errors.append(line)

            # 通用错误模式
            elif any(pattern in line.lower() for pattern in ['error:', 'failed:', 'exception:', 'traceback']):
                errors.append(line)

        return errors

    def _extract_test_errors(self, job_log: str) -> List[str]:
        """提取测试错误"""
        errors = []
        lines = job_log.split('\n')

        for line in lines:
            line = line.strip()
            if not line:
                continue

            # pytest错误模式
            if any(pattern in line.lower() for pattern in ['failed', 'error', 'assertion']):
                if any(test_pattern in line.lower() for test_pattern in ['test_', '.py::', 'pytest']):
                    errors.append(line)

            # unittest错误模式
            elif 'unittest' in line.lower() and 'failed' in line.lower():
                errors.append(line)

        return errors

    def _extract_build_errors(self, job_log: str) -> List[str]:
        """提取构建错误"""
        errors = []
        lines = job_log.split('\n')

        for line in lines:
            line = line.strip()
            if not line:
                continue

            # 构建错误模式
            if any(pattern in line.lower() for pattern in [
                'build failed', 'compilation error', 'missing dependency',
                'module not found', 'import error', 'syntax error'
            ]):
                errors.append(line)

        return errors

    def _extract_generic_errors(self, job_log: str) -> List[str]:
        """提取通用错误"""
        errors = []
        lines = job_log.split('\n')

        for line in lines:
            line = line.strip()
            if not line:
                continue

            # 通用错误模式
            if any(pattern in line.lower() for pattern in [
                'error:', 'exception:', 'failed:', 'traceback',
                'fatal:', 'critical:', 'abort'
            ]):
                errors.append(line)

            # pip和requirements.txt错误
            elif any(pattern in line.lower() for pattern in [
                'invalid requirement', 'pip install failed',
                'could not find a version', 'no matching distribution',
                'requirements.txt'
            ]):
                errors.append(line)

            # 构建和依赖错误
            elif any(pattern in line.lower() for pattern in [
                'modulenotfounderror', 'importerror', 'no module named',
                'job failed: exit code'
            ]):
                errors.append(line)

        return errors

    def _categorize_errors(self, errors: List[str]) -> Dict[str, List[str]]:
        """错误分类"""
        categories = {
            'syntax_errors': [],
            'dependency_errors': [],
            'test_failures': [],
            'lint_errors': [],
            'build_errors': [],
            'other_errors': []
        }

        for error in errors:
            # 处理字典类型的错误
            if isinstance(error, dict):
                error_content = error.get('content', '')
                error_type = error.get('type', '')
                error_category = error.get('category', '')

                # 优先使用已有的分类信息
                if error_category == 'dependency':
                    categories['dependency_errors'].append(error)
                elif error_category == 'build':
                    categories['build_errors'].append(error)
                elif error_type == 'pip_error':
                    categories['dependency_errors'].append(error)
                elif error_type == 'build_error':
                    categories['build_errors'].append(error)
                else:
                    # 基于内容进行分类
                    error_lower = error_content.lower()
                    if 'syntaxerror' in error_lower or 'invalid syntax' in error_lower:
                        categories['syntax_errors'].append(error)
                    elif any(pattern in error_lower for pattern in [
                        'modulenotfounderror', 'importerror', 'no module named',
                        'invalid requirement', 'pip install failed', 'requirements.txt'
                    ]):
                        categories['dependency_errors'].append(error)
                    elif 'test' in error_lower and ('failed' in error_lower or 'error' in error_lower):
                        categories['test_failures'].append(error)
                    elif any(lint_tool in error_lower for lint_tool in ['flake8', 'pylint', 'black', 'would reformat']):
                        categories['lint_errors'].append(error)
                    elif any(pattern in error_lower for pattern in [
                        'build failed', 'compilation error', 'job failed: exit code'
                    ]):
                        categories['build_errors'].append(error)
                    else:
                        categories['other_errors'].append(error)
            else:
                # 处理字符串类型的错误
                error_lower = str(error).lower()
                if 'syntaxerror' in error_lower or 'invalid syntax' in error_lower:
                    categories['syntax_errors'].append(error)
                elif any(pattern in error_lower for pattern in [
                    'modulenotfounderror', 'importerror', 'no module named',
                    'invalid requirement', 'pip install failed', 'requirements.txt'
                ]):
                    categories['dependency_errors'].append(error)
                elif 'test' in error_lower and ('failed' in error_lower or 'error' in error_lower):
                    categories['test_failures'].append(error)
                elif any(lint_tool in error_lower for lint_tool in ['flake8', 'pylint', 'black', 'would reformat']):
                    categories['lint_errors'].append(error)
                elif any(pattern in error_lower for pattern in [
                    'build failed', 'compilation error', 'job failed: exit code'
                ]):
                    categories['build_errors'].append(error)
                else:
                    categories['other_errors'].append(error)

        return {k: v for k, v in categories.items() if v}  # 只返回非空分类

    def _extract_success_status(self, result: Any) -> bool:
        """
        从不同类型的结果对象中提取成功状态

        Args:
            result: 可能是dict、ToolResult对象或其他类型

        Returns:
            bool: 成功状态
        """
        if isinstance(result, dict):
            return result.get('success', False)
        elif hasattr(result, 'success'):
            return getattr(result, 'success', False)
        else:
            # 对于其他类型，默认为False
            return False

    def _extract_message(self, result: Any) -> str:
        """
        从不同类型的结果对象中提取消息

        Args:
            result: 可能是dict、ToolResult对象或其他类型

        Returns:
            str: 消息内容
        """
        if isinstance(result, dict):
            return result.get('message', '')
        elif hasattr(result, 'message'):
            return getattr(result, 'message', '')
        else:
            return str(result)

    async def _fix_syntax_errors(self, errors: List[str], project_path: str) -> Dict[str, Any]:
        """修复语法错误"""
        # 实现语法错误修复逻辑
        return {'success': True, 'message': f'尝试修复 {len(errors)} 个语法错误'}

    async def _fix_dependency_errors(self, errors: List[str], project_path: str) -> Dict[str, Any]:
        """修复依赖错误"""
        try:
            logger.info(f"🔧 修复依赖错误: {len(errors)} 个")

            fixed_count = 0
            fix_details = []

            for error in errors:
                # 处理字典类型的错误
                if isinstance(error, dict):
                    error_content = error.get('content', '')
                    error_type = error.get('type', '')
                else:
                    error_content = str(error)
                    error_type = ''

                error_lower = error_content.lower()

                # 修复requirements.txt中的无效依赖声明
                if 'invalid requirement' in error_lower and 'requirements.txt' in error_lower:
                    # 提取行号
                    import re
                    line_match = re.search(r'line\s+(\d+)', error_content)
                    if line_match:
                        line_num = int(line_match.group(1))
                        fix_result = await self._fix_requirements_txt(project_path, line_num, error_content)
                        if fix_result:
                            fixed_count += 1
                            fix_details.append(f"修复requirements.txt第{line_num}行")

                # 修复缺失的模块
                elif any(pattern in error_lower for pattern in ['modulenotfounderror', 'no module named']):
                    # 提取模块名
                    module_match = re.search(r"no module named ['\"]([^'\"]+)['\"]", error_lower)
                    if module_match:
                        module_name = module_match.group(1)
                        fix_result = await self._install_missing_module(project_path, module_name)
                        if fix_result:
                            fixed_count += 1
                            fix_details.append(f"安装缺失模块: {module_name}")

            return {
                'success': fixed_count > 0,
                'message': f'修复了 {fixed_count}/{len(errors)} 个依赖错误',
                'details': fix_details,
                'fixed_count': fixed_count
            }

        except Exception as e:
            logger.error(f"修复依赖错误失败: {e}")
            return {
                'success': False,
                'message': f'修复依赖错误失败: {str(e)}',
                'details': [],
                'fixed_count': 0
            }

    async def _fix_test_failures(self, errors: List[str], project_path: str) -> Dict[str, Any]:
        """修复测试失败"""
        # 实现测试失败修复逻辑
        return {'success': True, 'message': f'尝试修复 {len(errors)} 个测试失败'}

    async def _fix_lint_errors(self, errors: List[str], project_path: str) -> Dict[str, Any]:
        """修复代码规范错误"""
        try:
            logger.info(f"🔧 修复代码规范错误: {len(errors)} 个")

            fixed_count = 0
            fix_details = []

            for error in errors:
                # 处理字典类型的错误
                if isinstance(error, dict):
                    error_content = error.get('content', '')
                else:
                    error_content = str(error)

                error_lower = error_content.lower()

                # 修复Black格式错误
                if 'would reformat' in error_lower and '.py' in error_lower:
                    # 提取需要格式化的文件
                    import re
                    file_match = re.search(r'would reformat ([^\s]+\.py)', error_content)
                    if file_match:
                        file_path = file_match.group(1)
                        # 转换为相对路径
                        if file_path.startswith('/builds/'):
                            # GitLab CI路径格式：/builds/Longer/ai-proxy/api_proxy/utils.py
                            parts = file_path.split('/')
                            if len(parts) >= 4:
                                relative_path = '/'.join(parts[4:])  # 取api_proxy/utils.py部分
                            else:
                                relative_path = file_path
                        else:
                            relative_path = file_path

                        fix_result = await self._fix_black_format(project_path, relative_path)
                        if fix_result:
                            fixed_count += 1
                            fix_details.append(f"格式化文件: {relative_path}")



                # 修复flake8代码错误
                elif any(pattern in error_lower for pattern in ['flake8', 'pycodestyle', 'pyflakes']):
                    # 提取文件和错误信息
                    file_match = re.search(r'([^\s]+\.py):\d+:\d+:', error_content)
                    if file_match:
                        file_path = file_match.group(1)
                        fix_result = await self._fix_flake8_issues(project_path, file_path, error_content)
                        if fix_result:
                            fixed_count += 1
                            fix_details.append(f"修复flake8问题: {file_path}")

            return {
                'success': fixed_count > 0,
                'message': f'修复了 {fixed_count}/{len(errors)} 个代码规范错误',
                'details': fix_details,
                'fixed_count': fixed_count
            }

        except Exception as e:
            logger.error(f"修复代码规范错误失败: {e}")
            return {
                'success': False,
                'message': f'修复代码规范错误失败: {str(e)}',
                'details': [],
                'fixed_count': 0
            }

    async def _fix_build_errors(self, errors: List[str], project_path: str) -> Dict[str, Any]:
        """修复构建错误"""
        try:
            logger.info(f"🔧 修复构建错误: {len(errors)} 个")

            fixed_count = 0
            fix_details = []

            for error in errors:
                # 处理字典类型的错误
                if isinstance(error, dict):
                    error_content = error.get('content', '')
                else:
                    error_content = str(error)

                error_lower = error_content.lower()

                # 处理依赖相关的构建错误
                if any(pattern in error_lower for pattern in [
                    'invalid requirement', 'pip install failed', 'requirements.txt'
                ]):
                    # 委托给依赖错误修复
                    result = await self._fix_dependency_errors([error], project_path)
                    if result.get('success', False):
                        fixed_count += result.get('fixed_count', 0)
                        fix_details.extend(result.get('details', []))

            return {
                'success': fixed_count > 0,
                'message': f'修复了 {fixed_count}/{len(errors)} 个构建错误',
                'details': fix_details,
                'fixed_count': fixed_count
            }

        except Exception as e:
            logger.error(f"修复构建错误失败: {e}")
            return {
                'success': False,
                'message': f'修复构建错误失败: {str(e)}',
                'details': [],
                'fixed_count': 0
            }

    async def _fix_generic_errors(self, errors: List[str], project_path: str) -> Dict[str, Any]:
        """修复通用错误"""
        # 实现通用错误修复逻辑
        return {'success': True, 'message': f'尝试修复 {len(errors)} 个通用错误'}

    async def _fix_requirements_txt(self, project_path: str, line_num: int, error: str) -> bool:
        """修复requirements.txt文件中的错误"""
        try:
            import os
            requirements_path = os.path.join(project_path, 'requirements.txt')

            if not os.path.exists(requirements_path):
                logger.warning(f"requirements.txt文件不存在: {requirements_path}")
                return False

            # 读取文件内容
            with open(requirements_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            if line_num > len(lines):
                logger.warning(f"行号超出范围: {line_num} > {len(lines)}")
                return False

            # 获取错误行
            error_line = lines[line_num - 1].strip()
            logger.info(f"修复requirements.txt第{line_num}行: {error_line}")

            # 常见的修复模式
            fixed_line = error_line

            # 修复 [dev] 这种无效的依赖声明
            if error_line == '[dev]':
                # 这通常是额外依赖的错误写法，应该删除或注释
                fixed_line = f"# {error_line}  # 无效的依赖声明，已注释"
                logger.info(f"修复无效依赖声明: {error_line} -> {fixed_line}")

            # 修复其他常见错误
            elif error_line.startswith('[') and error_line.endswith(']'):
                # 可能是额外依赖的错误格式
                fixed_line = f"# {error_line}  # 可能的额外依赖，需要正确格式"
                logger.info(f"修复可能的额外依赖: {error_line} -> {fixed_line}")

            # 应用修复
            if fixed_line != error_line:
                lines[line_num - 1] = fixed_line + '\n'

                # 写回文件
                with open(requirements_path, 'w', encoding='utf-8') as f:
                    f.writelines(lines)

                logger.info(f"✅ 成功修复requirements.txt第{line_num}行")
                return True

            return False

        except Exception as e:
            logger.error(f"修复requirements.txt失败: {e}")
            return False

    async def _install_missing_module(self, project_path: str, module_name: str) -> bool:
        """安装缺失的模块"""
        try:
            logger.info(f"尝试安装缺失模块: {module_name}")

            # 使用terminal工具执行pip install
            result = await self.terminal.execute_command(
                f"pip install {module_name}",
                cwd=project_path
            )

            if result and result.success:
                logger.info(f"✅ 成功安装模块: {module_name}")
                return True
            else:
                logger.warning(f"安装模块失败: {module_name}")
                return False

        except Exception as e:
            logger.error(f"安装模块异常: {e}")
            return False

    async def _fix_black_format(self, project_path: str, file_path: str) -> bool:
        """修复Black格式问题"""
        try:
            import os

            # 构建完整的文件路径
            full_path = os.path.join(project_path, file_path)

            if not os.path.exists(full_path):
                logger.warning(f"文件不存在: {full_path}")
                return False

            logger.info(f"🎨 格式化文件: {file_path}")

            # 使用black格式化文件
            result = await self.terminal.execute_command(
                f'black "{file_path}"',
                cwd=project_path
            )

            if result and result.success:
                logger.info(f"✅ 成功格式化文件: {file_path}")

                # 验证格式化结果
                verify_result = await self.terminal.execute_command(
                    f'black --check "{file_path}"',
                    cwd=project_path
                )

                if verify_result and verify_result.success:
                    logger.info(f"✅ 格式化验证通过: {file_path}")
                    return True
                else:
                    logger.warning(f"⚠️ 格式化验证失败: {file_path}")
                    return False
            else:
                logger.warning(f"格式化失败: {file_path}")
                return False

        except Exception as e:
            logger.error(f"格式化文件异常: {e}")
            return False



    async def _fix_flake8_issues(self, project_path: str, file_path: str, error_content: str) -> bool:
        """修复flake8问题"""
        try:
            import os

            # 构建完整的文件路径
            full_path = os.path.join(project_path, file_path)

            if not os.path.exists(full_path):
                logger.warning(f"文件不存在: {full_path}")
                return False

            logger.info(f"🔧 修复flake8问题: {file_path}")

            # 分析具体的flake8错误类型
            if 'line too long' in error_content.lower():
                # 使用black自动修复长行问题
                result = await self._fix_black_format(project_path, file_path)
                return result

            elif 'unused import' in error_content.lower():
                # 使用autoflake移除未使用的导入
                result = await self.terminal.execute_command(
                    f'autoflake --remove-unused-variables --remove-all-unused-imports --in-place "{file_path}"',
                    cwd=project_path
                )
                return result and result.success

            elif 'trailing whitespace' in error_content.lower():
                # 移除尾随空格
                result = await self.terminal.execute_command(
                    f'sed -i "s/[[:space:]]*$//" "{file_path}"',
                    cwd=project_path
                )
                return result and result.success

            else:
                # 对于其他问题，尝试使用autopep8
                result = await self.terminal.execute_command(
                    f'autopep8 --in-place --aggressive --aggressive "{file_path}"',
                    cwd=project_path
                )
                return result and result.success

        except Exception as e:
            logger.error(f"修复flake8问题异常: {e}")
            return False


class SyncToolCoordinator:
    """
    同步工具协调器包装器
    为同步上下文提供异步工具的访问能力
    """

    def __init__(self):
        self.async_coordinator = IntelligentToolCoordinator()
        self._loop = None
        self._thread = None
        self._setup_event_loop()

    def _setup_event_loop(self):
        """设置独立的事件循环"""
        def run_loop():
            self._loop = asyncio.new_event_loop()
            asyncio.set_event_loop(self._loop)
            self._loop.run_forever()

        self._thread = threading.Thread(target=run_loop, daemon=True)
        self._thread.start()

        # 等待事件循环启动
        import time
        time.sleep(0.1)

    def _run_async(self, coro):
        """在独立的事件循环中运行异步函数"""
        if self._loop is None:
            self._setup_event_loop()

        future = asyncio.run_coroutine_threadsafe(coro, self._loop)
        return future.result(timeout=30)  # 30秒超时

    def get_job_info_and_log(self, job_id: int, project_id: int) -> ToolResult:
        """同步版本：获取作业信息和日志"""
        try:
            return self._run_async(self.async_coordinator.get_job_info_and_log(job_id, project_id))
        except Exception as e:
            logger.error(f"同步获取作业信息失败: {e}")
            return ToolResult(
                success=False,
                message=f"同步获取失败: {str(e)}",
                data=None
            )

    def analyze_job_errors(self, job_log: str, job_info: Dict[str, Any]) -> ToolResult:
        """同步版本：智能分析作业错误"""
        try:
            return self._run_async(self.async_coordinator.analyze_job_errors(job_log, job_info))
        except Exception as e:
            logger.error(f"同步错误分析失败: {e}")
            return ToolResult(
                success=False,
                message=f"同步分析失败: {str(e)}",
                data=None
            )

    def execute_targeted_fixes(self, error_analysis: Dict[str, Any], project_path: str) -> ToolResult:
        """同步版本：执行针对性修复"""
        try:
            return self._run_async(self.async_coordinator.execute_targeted_fixes(error_analysis, project_path))
        except Exception as e:
            logger.error(f"同步执行修复失败: {e}")
            return ToolResult(
                success=False,
                message=f"同步修复失败: {str(e)}",
                data=None
            )

    def verify_fixes(self, project_path: str, job_type: str) -> ToolResult:
        """同步版本：验证修复效果"""
        try:
            return self._run_async(self.async_coordinator.verify_fixes(project_path, job_type))
        except Exception as e:
            logger.error(f"同步验证失败: {e}")
            return ToolResult(
                success=False,
                message=f"同步验证失败: {str(e)}",
                data=None
            )


# 全局实例
global_tool_coordinator = SyncToolCoordinator()
