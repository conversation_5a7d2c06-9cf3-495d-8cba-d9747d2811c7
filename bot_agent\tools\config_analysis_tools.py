"""
配置文件分析工具 - 为AI提供配置文件分析和修复能力
"""

import os
import re
import json
import configparser
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path

from .base_tool import BaseTool, ToolResult


class ConfigAnalysisTools(BaseTool):
    """
    配置文件分析工具
    
    为AI提供分析和修复各种配置文件的能力，包括：
    - .flake8配置文件
    - setup.cfg
    - pyproject.toml
    - requirements.txt
    - 其他INI格式配置文件
    """
    
    def __init__(self):
        super().__init__()
        self.supported_formats = {
            '.flake8': self._analyze_flake8_config,
            'setup.cfg': self._analyze_setup_cfg,
            'pyproject.toml': self._analyze_pyproject_toml,
            'requirements.txt': self._analyze_requirements_txt,
            'tox.ini': self._analyze_ini_config,
            '.ini': self._analyze_ini_config
        }
    
    async def execute(self, action: str, **kwargs) -> ToolResult:
        """
        执行配置分析操作
        
        Args:
            action: 操作类型 ('analyze', 'validate', 'suggest_fix')
            **kwargs: 其他参数
        """
        try:
            if action == 'analyze':
                return await self.analyze_config_file(
                    kwargs.get('file_path'),
                    kwargs.get('project_path')
                )
            elif action == 'validate':
                return await self.validate_config_syntax(
                    kwargs.get('file_path'),
                    kwargs.get('content')
                )
            elif action == 'suggest_fix':
                return await self.suggest_config_fix(
                    kwargs.get('file_path'),
                    kwargs.get('error_message'),
                    kwargs.get('project_path')
                )
            else:
                return ToolResult(
                    success=False,
                    error=f"不支持的操作: {action}"
                )
        except Exception as e:
            return ToolResult(
                success=False,
                error=f"配置分析失败: {str(e)}"
            )
    
    async def analyze_config_file(self, file_path: str, project_path: str = None) -> ToolResult:
        """
        分析配置文件
        
        Args:
            file_path: 配置文件路径
            project_path: 项目根路径
            
        Returns:
            ToolResult: 分析结果
        """
        try:
            if not os.path.exists(file_path):
                return ToolResult(
                    success=False,
                    error=f"配置文件不存在: {file_path}"
                )
            
            file_name = os.path.basename(file_path)
            
            # 选择合适的分析器
            analyzer = None
            for pattern, func in self.supported_formats.items():
                if pattern in file_name or file_name.endswith(pattern):
                    analyzer = func
                    break
            
            if not analyzer:
                return ToolResult(
                    success=False,
                    error=f"不支持的配置文件格式: {file_name}"
                )
            
            # 读取文件内容
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 执行分析
            analysis_result = analyzer(content, file_path)
            
            return ToolResult(
                success=True,
                data={
                    'file_path': file_path,
                    'file_name': file_name,
                    'content': content,
                    'analysis': analysis_result,
                    'line_count': len(content.splitlines()),
                    'size': len(content)
                },
                message=f"成功分析配置文件: {file_name}"
            )
            
        except Exception as e:
            return ToolResult(
                success=False,
                error=f"分析配置文件失败: {str(e)}"
            )
    
    async def validate_config_syntax(self, file_path: str, content: str = None) -> ToolResult:
        """
        验证配置文件语法
        
        Args:
            file_path: 配置文件路径
            content: 文件内容（可选，如果不提供则从文件读取）
            
        Returns:
            ToolResult: 验证结果
        """
        try:
            if content is None:
                if not os.path.exists(file_path):
                    return ToolResult(
                        success=False,
                        error=f"配置文件不存在: {file_path}"
                    )
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
            
            file_name = os.path.basename(file_path)
            validation_errors = []
            
            # 根据文件类型进行语法验证
            if '.flake8' in file_name:
                validation_errors = self._validate_flake8_syntax(content)
            elif 'setup.cfg' in file_name or file_name.endswith('.ini'):
                validation_errors = self._validate_ini_syntax(content)
            elif 'pyproject.toml' in file_name:
                validation_errors = self._validate_toml_syntax(content)
            elif 'requirements.txt' in file_name:
                validation_errors = self._validate_requirements_syntax(content)
            
            return ToolResult(
                success=len(validation_errors) == 0,
                data={
                    'file_path': file_path,
                    'validation_errors': validation_errors,
                    'is_valid': len(validation_errors) == 0,
                    'error_count': len(validation_errors)
                },
                message=f"语法验证{'通过' if len(validation_errors) == 0 else '失败'}: {file_name}"
            )
            
        except Exception as e:
            return ToolResult(
                success=False,
                error=f"语法验证失败: {str(e)}"
            )
    
    async def suggest_config_fix(self, file_path: str, error_message: str, project_path: str = None) -> ToolResult:
        """
        基于错误信息建议配置修复方案
        
        Args:
            file_path: 配置文件路径
            error_message: 错误信息
            project_path: 项目根路径
            
        Returns:
            ToolResult: 修复建议
        """
        try:
            file_name = os.path.basename(file_path)
            suggestions = []
            
            # 基于错误信息生成修复建议
            if 'extend-ignore' in error_message and 'error code' in error_message.lower():
                # flake8配置错误
                invalid_code_match = re.search(r"Error code '([^']+)' supplied to 'extend-ignore'", error_message)
                if invalid_code_match:
                    invalid_code = invalid_code_match.group(1)
                    suggestions.append({
                        'type': 'flake8_extend_ignore_fix',
                        'description': f"移除extend-ignore选项中的无效字符: {invalid_code}",
                        'action': 'remove_invalid_error_code',
                        'target': invalid_code,
                        'file_section': 'flake8',
                        'priority': 'high'
                    })
            
            elif 'invalid requirement' in error_message.lower():
                # requirements.txt错误
                line_match = re.search(r'line\s+(\d+)', error_message)
                if line_match:
                    line_num = int(line_match.group(1))
                    suggestions.append({
                        'type': 'requirements_fix',
                        'description': f"修复requirements.txt第{line_num}行的无效依赖声明",
                        'action': 'fix_requirement_line',
                        'target_line': line_num,
                        'priority': 'high'
                    })
            
            elif 'syntax error' in error_message.lower():
                # 通用语法错误
                suggestions.append({
                    'type': 'syntax_fix',
                    'description': "修复配置文件语法错误",
                    'action': 'fix_syntax',
                    'priority': 'high'
                })
            
            return ToolResult(
                success=True,
                data={
                    'file_path': file_path,
                    'error_message': error_message,
                    'suggestions': suggestions,
                    'suggestion_count': len(suggestions)
                },
                message=f"生成了 {len(suggestions)} 个修复建议"
            )
            
        except Exception as e:
            return ToolResult(
                success=False,
                error=f"生成修复建议失败: {str(e)}"
            )
    
    def _analyze_flake8_config(self, content: str, file_path: str) -> Dict[str, Any]:
        """分析.flake8配置文件"""
        try:
            config = configparser.ConfigParser()
            config.read_string(content)
            
            analysis = {
                'format': 'flake8',
                'sections': list(config.sections()),
                'settings': {},
                'potential_issues': []
            }
            
            # 分析flake8设置
            if 'flake8' in config:
                flake8_section = config['flake8']
                analysis['settings'] = dict(flake8_section)
                
                # 检查extend-ignore设置
                if 'extend-ignore' in flake8_section:
                    extend_ignore = flake8_section['extend-ignore']
                    # 检查是否包含无效字符
                    invalid_chars = re.findall(r'[^A-Z0-9,\s]', extend_ignore)
                    if invalid_chars:
                        analysis['potential_issues'].append({
                            'type': 'invalid_error_codes',
                            'message': f"extend-ignore包含无效字符: {invalid_chars}",
                            'suggestion': "移除无效字符，只保留有效的错误代码"
                        })
            
            return analysis
            
        except Exception as e:
            return {
                'format': 'flake8',
                'error': f"解析失败: {str(e)}",
                'raw_content': content[:200] + '...' if len(content) > 200 else content
            }
    
    def _analyze_setup_cfg(self, content: str, file_path: str) -> Dict[str, Any]:
        """分析setup.cfg配置文件"""
        try:
            config = configparser.ConfigParser()
            config.read_string(content)
            
            return {
                'format': 'setup.cfg',
                'sections': list(config.sections()),
                'settings': {section: dict(config[section]) for section in config.sections()},
                'potential_issues': []
            }
            
        except Exception as e:
            return {
                'format': 'setup.cfg',
                'error': f"解析失败: {str(e)}",
                'raw_content': content[:200] + '...' if len(content) > 200 else content
            }
    
    def _analyze_pyproject_toml(self, content: str, file_path: str) -> Dict[str, Any]:
        """分析pyproject.toml配置文件"""
        try:
            # 简单的TOML解析（如果需要完整支持，可以使用toml库）
            return {
                'format': 'pyproject.toml',
                'content_preview': content[:200] + '...' if len(content) > 200 else content,
                'line_count': len(content.splitlines()),
                'potential_issues': []
            }
            
        except Exception as e:
            return {
                'format': 'pyproject.toml',
                'error': f"解析失败: {str(e)}",
                'raw_content': content[:200] + '...' if len(content) > 200 else content
            }
    
    def _analyze_requirements_txt(self, content: str, file_path: str) -> Dict[str, Any]:
        """分析requirements.txt文件"""
        lines = content.splitlines()
        analysis = {
            'format': 'requirements.txt',
            'total_lines': len(lines),
            'dependencies': [],
            'invalid_lines': [],
            'potential_issues': []
        }
        
        for i, line in enumerate(lines, 1):
            line = line.strip()
            if not line or line.startswith('#'):
                continue
            
            # 检查是否是有效的依赖声明
            if line.startswith('[') and line.endswith(']'):
                analysis['invalid_lines'].append({
                    'line_number': i,
                    'content': line,
                    'issue': '无效的依赖声明格式'
                })
            elif '==' in line or '>=' in line or '<=' in line or '>' in line or '<' in line:
                analysis['dependencies'].append({
                    'line_number': i,
                    'content': line,
                    'type': 'versioned_dependency'
                })
            else:
                analysis['dependencies'].append({
                    'line_number': i,
                    'content': line,
                    'type': 'simple_dependency'
                })
        
        if analysis['invalid_lines']:
            analysis['potential_issues'].append({
                'type': 'invalid_dependency_format',
                'message': f"发现 {len(analysis['invalid_lines'])} 行无效的依赖声明",
                'suggestion': "移除或修复无效的依赖声明行"
            })
        
        return analysis
    
    def _analyze_ini_config(self, content: str, file_path: str) -> Dict[str, Any]:
        """分析INI格式配置文件"""
        try:
            config = configparser.ConfigParser()
            config.read_string(content)
            
            return {
                'format': 'ini',
                'sections': list(config.sections()),
                'settings': {section: dict(config[section]) for section in config.sections()},
                'potential_issues': []
            }
            
        except Exception as e:
            return {
                'format': 'ini',
                'error': f"解析失败: {str(e)}",
                'raw_content': content[:200] + '...' if len(content) > 200 else content
            }
    
    def _validate_flake8_syntax(self, content: str) -> List[Dict[str, Any]]:
        """验证flake8配置语法"""
        errors = []
        try:
            config = configparser.ConfigParser()
            config.read_string(content)
            
            # 检查extend-ignore语法
            if 'flake8' in config and 'extend-ignore' in config['flake8']:
                extend_ignore = config['flake8']['extend-ignore']
                # 检查错误代码格式
                codes = [code.strip() for code in extend_ignore.split(',')]
                for code in codes:
                    if code and not re.match(r'^[A-Z]{1,3}[0-9]{0,3}$', code):
                        errors.append({
                            'type': 'invalid_error_code',
                            'message': f"无效的错误代码格式: {code}",
                            'line': None,
                            'suggestion': "错误代码应该是字母+数字的格式，如E501, W503"
                        })
            
        except configparser.Error as e:
            errors.append({
                'type': 'syntax_error',
                'message': f"配置文件语法错误: {str(e)}",
                'line': None,
                'suggestion': "检查配置文件的INI格式语法"
            })
        
        return errors
    
    def _validate_ini_syntax(self, content: str) -> List[Dict[str, Any]]:
        """验证INI文件语法"""
        errors = []
        try:
            config = configparser.ConfigParser()
            config.read_string(content)
        except configparser.Error as e:
            errors.append({
                'type': 'syntax_error',
                'message': f"INI文件语法错误: {str(e)}",
                'line': None,
                'suggestion': "检查INI文件格式"
            })
        
        return errors
    
    def _validate_toml_syntax(self, content: str) -> List[Dict[str, Any]]:
        """验证TOML文件语法"""
        errors = []
        # 简单的TOML语法检查
        # 如果需要完整支持，可以使用toml库
        return errors
    
    def _validate_requirements_syntax(self, content: str) -> List[Dict[str, Any]]:
        """验证requirements.txt语法"""
        errors = []
        lines = content.splitlines()
        
        for i, line in enumerate(lines, 1):
            line = line.strip()
            if not line or line.startswith('#'):
                continue
            
            # 检查无效格式
            if line.startswith('[') and line.endswith(']'):
                errors.append({
                    'type': 'invalid_requirement',
                    'message': f"第{i}行: 无效的依赖声明格式",
                    'line': i,
                    'content': line,
                    'suggestion': "移除或修复此行"
                })
        
        return errors


# 全局实例
config_analysis_tools = ConfigAnalysisTools()
