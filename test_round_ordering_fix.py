#!/usr/bin/env python3
"""
测试轮次排序修复效果

这个脚本测试修复后的轮次显示顺序是否正确。
"""

import os
import sys
import tempfile
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_conversation_logger_sorting():
    """测试对话记录器的轮次排序"""
    print("🧪 测试对话记录器的轮次排序...")
    
    try:
        from bot_agent.utils.conversation_logger import global_conversation_logger, ConversationStatus
        
        # 创建测试会话
        session_id = global_conversation_logger.start_session(
            task_id="test_round_ordering",
            task_title="测试轮次排序修复",
            task_type="test",
            project_path=os.getcwd(),
            metadata={"test": True}
        )
        
        print(f"✅ 创建测试会话: {session_id}")
        
        # 故意以错误的顺序记录轮次（模拟实际问题）
        test_rounds = [
            {"round_number": 3, "round_name": "第三轮", "delay": 0.1},
            {"round_number": 1, "round_name": "第一轮", "delay": 0.2},
            {"round_number": 2, "round_name": "第二轮", "delay": 0.3},
            {"round_number": 5, "round_name": "第五轮", "delay": 0.4},
            {"round_number": 4, "round_name": "第四轮", "delay": 0.5},
        ]
        
        # 按错误顺序记录轮次
        for round_info in test_rounds:
            time.sleep(round_info["delay"])  # 确保时间戳不同
            global_conversation_logger.log_round(
                session_id=session_id,
                round_number=round_info["round_number"],
                round_name=round_info["round_name"],
                prompt=f"测试提示词 - {round_info['round_name']}",
                response=f"测试响应 - {round_info['round_name']}",
                model_name="test-model",
                duration=1.0,
                status=ConversationStatus.SUCCESS
            )
            print(f"📝 记录轮次 {round_info['round_number']}: {round_info['round_name']}")
        
        # 结束会话
        global_conversation_logger.end_session(
            session_id=session_id,
            final_result="测试完成",
            status=ConversationStatus.SUCCESS
        )
        
        # 检查生成的Markdown文件中的轮次顺序
        from datetime import datetime
        today = datetime.now().strftime("%Y-%m-%d")
        md_file = global_conversation_logger.log_dir / today / f"{session_id}.md"
        
        if md_file.exists():
            content = md_file.read_text(encoding='utf-8')
            
            # 检查轮次顺序
            round_positions = []
            for i in range(1, 6):
                pos = content.find(f"### 第{i}轮:")
                if pos != -1:
                    round_positions.append((i, pos))
            
            # 验证轮次是否按正确顺序排列
            round_positions.sort(key=lambda x: x[1])  # 按在文件中的位置排序
            expected_order = [1, 2, 3, 4, 5]
            actual_order = [round_num for round_num, _ in round_positions]
            
            print(f"📄 Markdown文件: {md_file.name}")
            print(f"📊 期望顺序: {expected_order}")
            print(f"📊 实际顺序: {actual_order}")
            
            if actual_order == expected_order:
                print("✅ Markdown文件中的轮次顺序正确！")
                return True
            else:
                print("❌ Markdown文件中的轮次顺序错误！")
                return False
        else:
            print("❌ 未找到Markdown文件")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_web_ui_api_sorting():
    """测试Web UI API的轮次排序"""
    print("\n🧪 测试Web UI API的轮次排序...")
    
    try:
        from bot_agent.utils.conversation_logger import global_conversation_logger
        
        # 查找最近的测试会话
        sessions = global_conversation_logger.search_sessions(task_type="test")
        if not sessions:
            print("⚠️ 未找到测试会话，跳过API测试")
            return True
        
        test_session = sessions[0]
        session_id = test_session["session_id"]
        
        # 加载会话详情
        session = global_conversation_logger._load_session_from_file(session_id)
        if not session:
            print("❌ 无法加载会话详情")
            return False
        
        # 检查轮次顺序
        round_numbers = [round_info.round_number for round_info in session.rounds]
        sorted_round_numbers = sorted(round_numbers)
        
        print(f"📊 原始轮次顺序: {round_numbers}")
        print(f"📊 排序后顺序: {sorted_round_numbers}")
        
        # 模拟Web UI的排序逻辑
        sorted_rounds = sorted(session.rounds, key=lambda r: r.round_number)
        web_ui_order = [round_info.round_number for round_info in sorted_rounds]
        
        print(f"📊 Web UI显示顺序: {web_ui_order}")
        
        if web_ui_order == sorted_round_numbers:
            print("✅ Web UI API轮次排序正确！")
            return True
        else:
            print("❌ Web UI API轮次排序错误！")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_specific_session_ordering():
    """测试特定会话的轮次排序"""
    print("\n🧪 测试特定会话 task_1748480883_1748480883 的轮次排序...")
    
    try:
        from bot_agent.utils.conversation_logger import global_conversation_logger
        
        # 加载特定会话
        session_id = "task_1748480883_1748480883"
        session = global_conversation_logger._load_session_from_file(session_id)
        
        if not session:
            print(f"❌ 无法找到会话 {session_id}")
            return False
        
        print(f"📋 会话ID: {session_id}")
        print(f"📋 轮次数量: {len(session.rounds)}")
        
        # 显示原始轮次顺序（按记录时间）
        print("\n📊 原始记录顺序（按时间）:")
        for i, round_info in enumerate(session.rounds):
            print(f"  {i+1}. 第{round_info.round_number}轮: {round_info.round_name} ({round_info.timestamp})")
        
        # 显示排序后的轮次顺序
        sorted_rounds = sorted(session.rounds, key=lambda r: r.round_number)
        print("\n📊 修复后显示顺序（按轮次编号）:")
        for i, round_info in enumerate(sorted_rounds):
            print(f"  {i+1}. 第{round_info.round_number}轮: {round_info.round_name} ({round_info.timestamp})")
        
        # 验证排序是否正确
        expected_order = [1, 2, 3]  # 期望的轮次顺序
        actual_order = [round_info.round_number for round_info in sorted_rounds]
        
        if actual_order == expected_order:
            print("✅ 特定会话的轮次排序修复成功！")
            return True
        else:
            print(f"❌ 轮次排序仍有问题，期望: {expected_order}，实际: {actual_order}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试轮次排序修复效果\n")
    
    tests = [
        test_conversation_logger_sorting,
        test_web_ui_api_sorting,
        test_specific_session_ordering
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()  # 添加空行分隔
    
    print("=" * 60)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！轮次排序问题已修复。")
        print("\n📝 修复总结:")
        print("1. ✅ 修复了对话记录器Markdown生成时的轮次排序")
        print("2. ✅ 修复了Web UI会话详情页面的轮次显示顺序")
        print("3. ✅ 确保轮次按编号而非时间顺序显示")
        return True
    else:
        print("⚠️ 部分测试失败，需要进一步检查。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
