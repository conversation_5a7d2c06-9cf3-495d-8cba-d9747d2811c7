#!/usr/bin/env python3
"""
测试AI修复系统改进效果

这个脚本测试改进后的AI修复系统是否能够：
1. 正确处理ToolResult对象
2. 生成更好的AI修复方案
3. 智能判断修复完整性
4. 触发多轮修复机制
"""

import os
import sys
import asyncio
import tempfile
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

async def test_tool_result_handling():
    """测试ToolResult对象处理"""
    print("🧪 测试ToolResult对象处理...")
    
    try:
        from bot_agent.tools.intelligent_tool_coordinator import global_tool_coordinator
        
        # 创建模拟的ToolResult对象
        class MockToolResult:
            def __init__(self, success, message, data=None):
                self.success = success
                self.message = message
                self.data = data or {}
        
        # 测试_extract_success_status方法
        coordinator = global_tool_coordinator.async_coordinator
        
        # 测试dict类型
        dict_result = {'success': True, 'message': 'test'}
        success1 = coordinator._extract_success_status(dict_result)
        print(f"  ✅ Dict结果处理: {success1}")
        
        # 测试ToolResult对象
        tool_result = MockToolResult(True, 'test message')
        success2 = coordinator._extract_success_status(tool_result)
        print(f"  ✅ ToolResult对象处理: {success2}")
        
        # 测试_extract_message方法
        message1 = coordinator._extract_message(dict_result)
        message2 = coordinator._extract_message(tool_result)
        print(f"  ✅ 消息提取: dict='{message1}', tool='{message2}'")
        
        return True
        
    except Exception as e:
        print(f"  ❌ ToolResult处理测试失败: {e}")
        return False

async def test_config_analysis_tools():
    """测试配置文件分析工具"""
    print("\n🧪 测试配置文件分析工具...")
    
    try:
        from bot_agent.tools.config_analysis_tools import config_analysis_tools
        
        # 创建测试配置文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.flake8', delete=False) as f:
            f.write("""[flake8]
max-line-length = 88
extend-ignore = E203,W503,#,E501
exclude = .git,__pycache__,docs/source/conf.py,old,build,dist
""")
            test_config_path = f.name
        
        try:
            # 测试配置文件分析
            result = await config_analysis_tools.execute('analyze', 
                                                       file_path=test_config_path)
            
            if result.success:
                print(f"  ✅ 配置文件分析成功")
                analysis = result.data.get('analysis', {})
                issues = analysis.get('potential_issues', [])
                print(f"  📊 发现 {len(issues)} 个潜在问题")
                
                for issue in issues:
                    print(f"    - {issue.get('type', 'unknown')}: {issue.get('message', '')}")
            else:
                print(f"  ❌ 配置文件分析失败: {result.error}")
                return False
            
            # 测试语法验证
            validate_result = await config_analysis_tools.execute('validate',
                                                                file_path=test_config_path)
            
            if validate_result.success:
                errors = validate_result.data.get('validation_errors', [])
                print(f"  ✅ 语法验证完成，发现 {len(errors)} 个错误")
                
                for error in errors:
                    print(f"    - {error.get('type', 'unknown')}: {error.get('message', '')}")
            else:
                print(f"  ❌ 语法验证失败: {validate_result.error}")
                return False
            
            # 测试修复建议
            suggest_result = await config_analysis_tools.execute('suggest_fix',
                                                               file_path=test_config_path,
                                                               error_message="Error code '#' supplied to 'extend-ignore' option")
            
            if suggest_result.success:
                suggestions = suggest_result.data.get('suggestions', [])
                print(f"  ✅ 修复建议生成成功，提供 {len(suggestions)} 个建议")
                
                for suggestion in suggestions:
                    print(f"    - {suggestion.get('description', '')}")
            else:
                print(f"  ❌ 修复建议生成失败: {suggest_result.error}")
                return False
            
            return True
            
        finally:
            # 清理测试文件
            os.unlink(test_config_path)
        
    except Exception as e:
        print(f"  ❌ 配置分析工具测试失败: {e}")
        return False

async def test_fix_completeness_evaluation():
    """测试修复完整性评估"""
    print("\n🧪 测试修复完整性评估...")
    
    try:
        from bot_agent.engines.task_executor import TaskExecutor
        
        executor = TaskExecutor()
        
        # 创建模拟的验证结果
        class MockVerifyResult:
            def __init__(self, success, data=None):
                self.success = success
                self.data = data or {}
        
        # 测试场景1：验证完全失败
        verify_result1 = MockVerifyResult(False)
        original_errors1 = {'all_errors': ['test error']}
        
        needs_fix1 = executor._evaluate_fix_completeness(verify_result1, original_errors1)
        print(f"  ✅ 验证失败场景: 需要额外修复 = {needs_fix1}")
        
        # 测试场景2：成功率过低
        verify_result2 = MockVerifyResult(True, {
            'success_rate': 0.5,
            'verification_results': [
                {'type': 'lint_check', 'success': False, 'message': 'flake8检查失败'},
                {'type': 'syntax_check', 'success': True, 'message': '语法检查通过'}
            ]
        })
        original_errors2 = {
            'all_errors': [
                {'content': 'Error code \'#\' supplied to \'extend-ignore\' option', 'type': 'config_error'}
            ]
        }
        
        needs_fix2 = executor._evaluate_fix_completeness(verify_result2, original_errors2)
        print(f"  ✅ 成功率过低场景: 需要额外修复 = {needs_fix2}")
        
        # 测试场景3：修复完成
        verify_result3 = MockVerifyResult(True, {
            'success_rate': 1.0,
            'verification_results': [
                {'type': 'lint_check', 'success': True, 'message': 'flake8检查通过'},
                {'type': 'syntax_check', 'success': True, 'message': '语法检查通过'}
            ]
        })
        original_errors3 = {'all_errors': ['some error']}
        
        needs_fix3 = executor._evaluate_fix_completeness(verify_result3, original_errors3)
        print(f"  ✅ 修复完成场景: 需要额外修复 = {needs_fix3}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 修复完整性评估测试失败: {e}")
        return False

async def test_remaining_errors_analysis():
    """测试剩余错误分析"""
    print("\n🧪 测试剩余错误分析...")
    
    try:
        from bot_agent.engines.task_executor import TaskExecutor
        
        executor = TaskExecutor()
        
        # 创建模拟的验证结果
        class MockVerifyResult:
            def __init__(self, success, data=None):
                self.success = success
                self.data = data or {}
        
        verify_result = MockVerifyResult(True, {
            'verification_results': [
                {'type': 'lint_check', 'success': False, 'message': 'flake8检查失败'},
                {'type': 'syntax_check', 'success': True, 'message': '语法检查通过'},
                {'type': 'dependency_check', 'success': False, 'message': '依赖检查失败'}
            ]
        })
        
        original_errors = {
            'all_errors': [
                {'content': 'Error code \'#\' supplied to \'extend-ignore\' option', 'type': 'config_error'},
                {'content': 'Invalid requirement: [dev]', 'type': 'dependency_error'},
                {'content': 'Syntax error in file.py', 'type': 'syntax_error'}
            ]
        }
        
        remaining_errors = executor._analyze_remaining_errors(verify_result, original_errors)
        
        print(f"  ✅ 剩余错误分析完成，发现 {len(remaining_errors)} 个剩余错误:")
        for i, error in enumerate(remaining_errors, 1):
            print(f"    {i}. {error}")
        
        # 测试格式化
        formatted = executor._format_remaining_errors(remaining_errors)
        print(f"  ✅ 错误格式化完成:\n{formatted}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 剩余错误分析测试失败: {e}")
        return False

async def test_enhanced_ai_prompt():
    """测试增强的AI提示词"""
    print("\n🧪 测试增强的AI提示词...")
    
    try:
        from bot_agent.tools.intelligent_tool_coordinator import global_tool_coordinator
        
        coordinator = global_tool_coordinator.async_coordinator
        
        # 测试错误格式化
        test_errors = [
            {'content': 'Error code \'#\' supplied to \'extend-ignore\' option', 'type': 'config_error'},
            'Invalid requirement: [dev] in requirements.txt',
            {'content': 'Syntax error in file.py', 'type': 'syntax_error'}
        ]
        
        formatted_errors = coordinator._format_errors_for_ai(test_errors)
        print(f"  ✅ 错误格式化完成:")
        print(f"    {formatted_errors}")
        
        # 验证格式化结果包含必要信息
        assert 'config_error' in formatted_errors
        assert 'extend-ignore' in formatted_errors
        assert 'Invalid requirement' in formatted_errors
        
        print(f"  ✅ 格式化结果验证通过")
        
        return True
        
    except Exception as e:
        print(f"  ❌ AI提示词测试失败: {e}")
        return False

async def main():
    """主测试函数"""
    print("🚀 开始测试AI修复系统改进效果\n")
    
    tests = [
        ("ToolResult对象处理", test_tool_result_handling),
        ("配置文件分析工具", test_config_analysis_tools),
        ("修复完整性评估", test_fix_completeness_evaluation),
        ("剩余错误分析", test_remaining_errors_analysis),
        ("增强的AI提示词", test_enhanced_ai_prompt),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if await test_func():
                passed += 1
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
        print()  # 添加空行分隔
    
    print("=" * 60)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！AI修复系统改进成功。")
        print("\n📝 改进总结:")
        print("1. ✅ 修复了ToolResult对象访问bug")
        print("2. ✅ 增强了配置文件分析能力")
        print("3. ✅ 改进了AI修复方案生成的提示词")
        print("4. ✅ 增强了验证机制和多轮修复触发")
        print("5. ✅ 提供了更好的错误分析和格式化")
        return True
    else:
        print("⚠️ 部分测试失败，需要进一步检查。")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
