#!/usr/bin/env python3
"""
测试重复轮次编号修复
验证Markdown格式化是否正确处理轮次标题
"""

import sys
import time
from pathlib import Path

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent))

def test_duplicate_round_fix():
    """测试重复轮次编号修复"""
    print("🔧 测试重复轮次编号修复")
    print("=" * 50)
    
    try:
        from bot_agent.utils.conversation_logger import global_conversation_logger
        
        # 创建测试会话
        session_id = global_conversation_logger.start_session(
            task_id="duplicate_round_test",
            task_title="重复轮次编号修复测试",
            task_type="test",
            project_path="/test/path"
        )
        
        print(f"✅ 会话ID: {session_id}")
        
        # 测试不同的轮次命名方式
        test_cases = [
            # 情况1：round_name已经包含轮次编号
            {
                "round_name": "第1轮测试：初始分析",
                "expected_title": "第1轮测试：初始分析"  # 不应该重复
            },
            # 情况2：round_name不包含轮次编号
            {
                "round_name": "AI生成修复方案",
                "expected_title": "第2轮：AI生成修复方案"  # 应该添加编号
            },
            # 情况3：round_name包含其他轮次编号
            {
                "round_name": "修复步骤1 - 配置文件",
                "expected_title": "第3轮：修复步骤1 - 配置文件"  # 应该添加编号
            },
            # 情况4：round_name已经包含当前轮次编号
            {
                "round_name": "第4轮：验证修复效果",
                "expected_title": "第4轮：验证修复效果"  # 不应该重复
            }
        ]
        
        # 记录测试轮次
        for i, test_case in enumerate(test_cases, 1):
            global_conversation_logger.log_round(
                session_id=session_id,
                round_name=test_case["round_name"],
                prompt=f"测试提示 {i}",
                response=f"测试响应 {i}",
                model_name="test-model",
                duration=0.1
            )
            print(f"✅ 记录轮次 {i}: {test_case['round_name']}")
        
        # 结束会话
        global_conversation_logger.end_session(session_id, "测试完成", "success")
        
        # 检查生成的Markdown文件
        print(f"\n📋 检查Markdown文件...")
        
        today = time.strftime("%Y-%m-%d")
        md_file = Path(f"logs/conversations/{today}/{session_id}.md")
        
        if md_file.exists():
            with open(md_file, 'r', encoding='utf-8') as f:
                md_content = f.read()
            
            print(f"📄 Markdown文件内容检查:")
            
            # 检查每个测试案例
            for i, test_case in enumerate(test_cases, 1):
                expected_title = test_case["expected_title"]
                
                if f"### {expected_title}" in md_content:
                    print(f"✅ 轮次 {i}: {expected_title}")
                else:
                    print(f"❌ 轮次 {i}: 期望 '{expected_title}' 但未找到")
                    
                    # 查找实际的标题
                    lines = md_content.split('\n')
                    for line in lines:
                        if line.startswith(f"### 第{i}轮"):
                            print(f"   实际标题: {line}")
                            break
            
            # 检查是否有重复编号
            duplicate_patterns = [
                "第1轮: 第1轮",
                "第2轮: 第2轮", 
                "第3轮: 第3轮",
                "第4轮: 第4轮"
            ]
            
            has_duplicates = False
            for pattern in duplicate_patterns:
                if pattern in md_content:
                    print(f"❌ 发现重复编号: {pattern}")
                    has_duplicates = True
            
            if not has_duplicates:
                print("✅ 没有发现重复编号")
            
            print(f"\n📊 测试结果:")
            if not has_duplicates:
                print("🎉 重复轮次编号修复成功！")
                print("- Markdown格式化正确处理轮次标题")
                print("- 不再有重复的轮次编号")
                return True
            else:
                print("❌ 仍然存在重复编号问题")
                return False
        else:
            print(f"❌ Markdown文件不存在: {md_file}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 开始重复轮次编号修复验证")
    
    result = test_duplicate_round_fix()
    
    if result:
        print(f"\n🎉 测试通过！重复轮次编号问题已修复！")
        print(f"现在Markdown文件中不会再出现 '第1轮: 第1轮测试' 这样的重复编号")
    else:
        print(f"\n❌ 测试失败，需要进一步修复")

if __name__ == "__main__":
    main()
