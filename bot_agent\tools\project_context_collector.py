"""
项目上下文收集器 - 复用现有功能版本
为AI多轮交互提供丰富的项目上下文信息，帮助AI更准确地生成修复命令
"""

import os
from typing import Dict, Any
from datetime import datetime

from bot_agent.utils.logging_config import get_logger

logger = get_logger(__name__)


class ProjectContextCollector:
    """
    项目上下文收集器 - 复用现有功能

    整合现有的工具来收集项目上下文信息：
    1. 复用 information_query_tools 的项目结构和配置分析
    2. 复用 terminal_tools 的环境信息收集
    3. 复用现有的Git信息查询功能
    4. 添加错误上下文整合
    """

    def __init__(self):
        self.context_cache = {}
        # 延迟导入，避免循环依赖
        self._info_query_tool = None
        self._terminal_tool = None

    def _get_info_query_tool(self):
        """延迟获取信息查询工具"""
        if self._info_query_tool is None:
            from bot_agent.tools.information_query_tools import InformationQueryTools
            self._info_query_tool = InformationQueryTools()
        return self._info_query_tool

    def _get_terminal_tool(self):
        """延迟获取终端工具"""
        if self._terminal_tool is None:
            from bot_agent.tools.terminal_tools import TerminalTools
            self._terminal_tool = TerminalTools()
        return self._terminal_tool

    async def collect_full_context(self, project_path: str, error_info: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        收集完整的项目上下文信息 - 复用现有功能

        Args:
            project_path: 项目路径
            error_info: 错误信息（可选）

        Returns:
            完整的上下文信息字典
        """
        try:
            logger.info(f"🔍 开始收集项目上下文信息: {project_path}")

            # 使用现有工具收集信息
            info_tool = self._get_info_query_tool()
            terminal_tool = self._get_terminal_tool()

            context = {
                'project_path': project_path,
                'collection_timestamp': self._get_timestamp(),
                'project_structure': await info_tool._query_project_structure(project_path),
                'configurations': await info_tool._query_configurations(project_path),
                'git_info': await info_tool._query_git_info(project_path),
                'environment_info': await terminal_tool.get_system_info(),
                'project_type': self._detect_project_type(project_path),
                'error_context': error_info or {}
            }

            # 缓存上下文信息
            self.context_cache[project_path] = context

            logger.info(f"✅ 项目上下文收集完成，共收集 {len(context)} 个维度的信息")
            return context

        except Exception as e:
            logger.error(f"项目上下文收集失败: {e}")
            return {
                'project_path': project_path,
                'error': str(e),
                'collection_timestamp': self._get_timestamp()
            }

    def _detect_project_type(self, project_path: str) -> Dict[str, Any]:
        """检测项目类型"""
        try:
            project_info = {
                'name': os.path.basename(project_path),
                'type': 'unknown',
                'language': 'unknown'
            }

            # 检测项目类型和语言
            if os.path.exists(os.path.join(project_path, 'requirements.txt')) or \
               os.path.exists(os.path.join(project_path, 'pyproject.toml')) or \
               os.path.exists(os.path.join(project_path, 'setup.py')):
                project_info['type'] = 'python'
                project_info['language'] = 'python'

            elif os.path.exists(os.path.join(project_path, 'package.json')):
                project_info['type'] = 'nodejs'
                project_info['language'] = 'javascript'

            elif os.path.exists(os.path.join(project_path, 'go.mod')):
                project_info['type'] = 'go'
                project_info['language'] = 'go'

            elif os.path.exists(os.path.join(project_path, 'Cargo.toml')):
                project_info['type'] = 'rust'
                project_info['language'] = 'rust'

            return project_info

        except Exception as e:
            logger.error(f"检测项目类型失败: {e}")
            return {'error': str(e)}

    def format_context_for_ai(self, context: Dict[str, Any], error_info: Dict[str, Any] = None) -> str:
        """
        将上下文信息格式化为AI友好的文本

        Args:
            context: 项目上下文信息
            error_info: 错误信息

        Returns:
            格式化的上下文文本
        """
        try:
            formatted_text = []

            # 项目基础信息
            if 'project_type' in context:
                project_info = context['project_type']
                formatted_text.append("## 📋 项目信息")
                formatted_text.append(f"- **项目名称**: {project_info.get('name', 'unknown')}")
                formatted_text.append(f"- **项目类型**: {project_info.get('type', 'unknown')}")
                formatted_text.append(f"- **编程语言**: {project_info.get('language', 'unknown')}")
                formatted_text.append("")

            # 运行环境信息
            if 'environment_info' in context:
                env_info = context['environment_info']
                env_data = {}

                # 安全地处理ToolResult对象或字典
                if hasattr(env_info, 'success') and hasattr(env_info, 'data'):
                    # 这是一个ToolResult对象
                    if env_info.success and env_info.data:
                        env_data = env_info.data if isinstance(env_info.data, dict) else {}
                elif isinstance(env_info, dict):
                    # 这是一个字典
                    if env_info.get('success', False):
                        env_data = env_info.get('data', {})
                        if not isinstance(env_data, dict):
                            env_data = {}
                    else:
                        env_data = env_info  # 直接使用字典数据

                if env_data:
                    formatted_text.append("## 🖥️ 运行环境")
                    formatted_text.append(f"- **操作系统**: {env_data.get('system', 'unknown')}")
                    formatted_text.append(f"- **Python版本**: {env_data.get('python_version', 'unknown')}")
                    formatted_text.append(f"- **平台**: {env_data.get('platform', 'unknown')}")
                    formatted_text.append("")

            # 项目结构
            if 'project_structure' in context:
                struct_info = context['project_structure']
                formatted_text.append("## 📁 项目结构")

                # 安全地处理ToolResult对象或字典
                struct_data = {}
                if hasattr(struct_info, 'success') and hasattr(struct_info, 'data'):
                    # 这是一个ToolResult对象
                    if struct_info.success and struct_info.data:
                        struct_data = struct_info.data if isinstance(struct_info.data, dict) else {}
                elif isinstance(struct_info, dict):
                    # 这是一个字典
                    struct_data = struct_info

                if struct_data and struct_data.get('summary'):
                    formatted_text.append(f"- **结构概览**: {struct_data['summary']}")
                formatted_text.append("")

            # 配置文件
            if 'configurations' in context:
                config_info = context['configurations']
                formatted_text.append("## ⚙️ 配置文件")

                # 安全地处理ToolResult对象或字典
                config_data = {}
                if hasattr(config_info, 'success') and hasattr(config_info, 'data'):
                    # 这是一个ToolResult对象
                    if config_info.success and config_info.data:
                        config_data = config_info.data if isinstance(config_info.data, dict) else {}
                elif isinstance(config_info, dict):
                    # 这是一个字典
                    config_data = config_info

                if config_data and config_data.get('summary'):
                    formatted_text.append(f"- **配置概览**: {config_data['summary']}")
                if config_data and config_data.get('configs'):
                    formatted_text.append("- **关键配置**:")
                    for config in config_data['configs'][:3]:  # 只显示前3个
                        if isinstance(config, dict):
                            formatted_text.append(f"  - {config.get('file', '')}: {config.get('description', '')}")
                        else:
                            formatted_text.append(f"  - {config}")
                formatted_text.append("")

            # Git信息
            if 'git_info' in context:
                git_info = context['git_info']
                formatted_text.append("## 🔄 Git信息")

                # 安全地处理ToolResult对象或字典
                git_data = {}
                if hasattr(git_info, 'success') and hasattr(git_info, 'data'):
                    # 这是一个ToolResult对象
                    if git_info.success and git_info.data:
                        git_data = git_info.data if isinstance(git_info.data, dict) else {}
                elif isinstance(git_info, dict):
                    # 这是一个字典
                    git_data = git_info

                if git_data and git_data.get('branch'):
                    formatted_text.append(f"- **当前分支**: {git_data['branch']}")
                if git_data and git_data.get('recent_commits'):
                    formatted_text.append("- **最近提交**: (显示最新的)")
                    commits = git_data['recent_commits'][:2] if isinstance(git_data['recent_commits'], list) else []
                    for commit in commits:
                        formatted_text.append(f"  - {commit}")
                formatted_text.append("")

            # 错误上下文
            if error_info:
                formatted_text.append("## ⚠️ 错误上下文")
                if 'errors' in error_info:
                    formatted_text.append("- **错误列表**:")
                    for error in error_info['errors'][:3]:  # 只显示前3个错误
                        formatted_text.append(f"  - {error}")
                formatted_text.append("")

            return '\n'.join(formatted_text)

        except Exception as e:
            logger.error(f"格式化上下文失败: {e}")
            return f"上下文格式化失败: {str(e)}"

    def _get_timestamp(self) -> str:
        """获取时间戳"""
        return datetime.now().isoformat()


# 全局实例
project_context_collector = ProjectContextCollector()
