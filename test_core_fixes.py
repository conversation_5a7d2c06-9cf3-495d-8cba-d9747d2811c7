#!/usr/bin/env python3
"""
测试核心问题修复效果
"""

import asyncio
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

async def test_toolresult_access_fix():
    """测试ToolResult访问错误修复"""
    print("🔧 测试ToolResult访问错误修复...")

    try:
        from bot_agent.tools.intelligent_tool_coordinator import IntelligentToolCoordinator
        from bot_agent.tools.base_tool import ToolResult

        coordinator = IntelligentToolCoordinator()

        # 模拟各种类型的错误对象
        test_errors = [
            # 字典类型错误
            {'type': 'config_error', 'content': 'flake8配置错误'},

            # ToolResult对象
            ToolResult(success=False, message="测试错误", data={'content': 'ToolResult错误内容'}),

            # 字符串错误
            "ValueError: Error code 'Ignore' supplied to 'extend-ignore' option",

            # 复杂错误对象
            ToolResult(success=True, data={'stdout': 'flake8检查失败', 'stderr': '配置错误'})
        ]

        # 测试错误格式化
        formatted_errors = coordinator._format_errors_for_ai(test_errors)
        print("✅ 错误格式化成功")
        print(f"格式化结果长度: {len(formatted_errors)} 字符")

        # 测试错误分类
        for error in test_errors:
            error_content = coordinator._extract_error_content(error)
            error_type = coordinator._classify_error_type(error_content)
            print(f"  - 错误类型: {error_type}")

        return True

    except Exception as e:
        print(f"❌ ToolResult访问错误修复测试失败: {e}")
        return False

async def test_round_numbering_fix():
    """测试轮次编号修复"""
    print("\n🔧 测试轮次编号修复...")

    try:
        from bot_agent.utils.conversation_logger import global_conversation_logger
        from bot_agent.utils.conversation_logger import ConversationStatus

        # 创建测试会话
        test_session_id = global_conversation_logger.start_session(
            task_id="test_task",
            task_title="测试轮次编号修复",
            task_type="test",
            project_path="/test/path"
        )

        # 模拟添加多个轮次
        for i in range(5):
            global_conversation_logger.log_round(
                session_id=test_session_id,
                round_number=i + 1,
                round_name=f"测试轮次{i + 1}",
                prompt=f"测试提示{i + 1}",
                response=f"测试响应{i + 1}",
                model_name="test-model",
                duration=1.0,
                status=ConversationStatus.SUCCESS
            )

        # 结束会话
        global_conversation_logger.end_session(test_session_id, status=ConversationStatus.SUCCESS)

        # 检查轮次编号是否连续
        session = global_conversation_logger._load_session_from_file(test_session_id)
        if session and session.rounds:
            round_numbers = [r.round_number for r in session.rounds]
            expected_numbers = list(range(1, len(round_numbers) + 1))

            if round_numbers == expected_numbers:
                print("✅ 轮次编号连续性测试通过")
                print(f"  - 轮次编号: {round_numbers}")
                return True
            else:
                print(f"❌ 轮次编号不连续: {round_numbers}")
                print(f"  - 期望编号: {expected_numbers}")
                return False
        else:
            print("❌ 无法加载测试会话")
            return False

    except Exception as e:
        print(f"❌ 轮次编号修复测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_ai_enhancement():
    """测试AI修复能力增强"""
    print("\n🔧 测试AI修复能力增强...")

    try:
        from bot_agent.tools.intelligent_tool_coordinator import IntelligentToolCoordinator

        coordinator = IntelligentToolCoordinator()

        # 测试错误分析能力
        test_error = "ValueError: Error code 'Ignore' supplied to 'extend-ignore' option does not match '^[A-Z]{1,3}[0-9]{0,3}$'"

        error_type = coordinator._classify_error_type(test_error)
        error_cause = coordinator._analyze_error_cause(test_error)
        error_priority = coordinator._get_error_priority(error_type)

        print(f"  - 错误分类: {error_type}")
        print(f"  - 错误原因: {error_cause}")
        print(f"  - 修复优先级: {error_priority}")

        # 验证分析结果
        if error_type == "配置文件错误" and "flake8" in error_cause and error_priority == "高":
            print("✅ AI错误分析能力增强测试通过")
            return True
        else:
            print("❌ AI错误分析结果不符合预期")
            return False

    except Exception as e:
        print(f"❌ AI修复能力增强测试失败: {e}")
        return False

async def main():
    """主测试函数"""
    print("🚀 开始核心问题修复测试\n")

    results = []

    # 测试1: ToolResult访问错误修复
    results.append(await test_toolresult_access_fix())

    # 测试2: 轮次编号修复
    results.append(await test_round_numbering_fix())

    # 测试3: AI修复能力增强
    results.append(await test_ai_enhancement())

    # 汇总结果
    print(f"\n📊 测试结果汇总:")
    print(f"  - 总测试数: {len(results)}")
    print(f"  - 通过数: {sum(results)}")
    print(f"  - 失败数: {len(results) - sum(results)}")
    print(f"  - 成功率: {sum(results) / len(results) * 100:.1f}%")

    if all(results):
        print("\n🎉 所有核心问题修复测试通过！")
        return True
    else:
        print("\n⚠️ 部分测试失败，需要进一步修复")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
