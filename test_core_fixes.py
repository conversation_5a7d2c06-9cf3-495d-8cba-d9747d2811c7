#!/usr/bin/env python3
"""
测试核心问题修复效果
"""

import asyncio
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

async def test_toolresult_access_fix():
    """测试ToolResult访问错误修复"""
    print("🔧 测试ToolResult访问错误修复...")

    try:
        from bot_agent.tools.intelligent_tool_coordinator import IntelligentToolCoordinator
        from bot_agent.tools.base_tool import ToolResult

        coordinator = IntelligentToolCoordinator()

        # 模拟各种类型的错误对象
        test_errors = [
            # 字典类型错误
            {'type': 'config_error', 'content': 'flake8配置错误'},

            # ToolResult对象
            ToolResult(success=False, message="测试错误", data={'content': 'ToolResult错误内容'}),

            # 字符串错误
            "ValueError: Error code 'Ignore' supplied to 'extend-ignore' option",

            # 复杂错误对象
            ToolResult(success=True, data={'stdout': 'flake8检查失败', 'stderr': '配置错误'})
        ]

        # 测试错误格式化
        formatted_errors = coordinator._format_errors_for_ai(test_errors)
        print("✅ 错误格式化成功")
        print(f"格式化结果长度: {len(formatted_errors)} 字符")

        # 测试错误分类
        for error in test_errors:
            error_content = coordinator._extract_error_content(error)
            error_type = coordinator._classify_error_type(error_content)
            print(f"  - 错误类型: {error_type}")

        return True

    except Exception as e:
        print(f"❌ ToolResult访问错误修复测试失败: {e}")
        return False

async def test_round_numbering_fix():
    """测试轮次编号修复"""
    print("\n🔧 测试轮次编号修复...")

    try:
        from bot_agent.utils.conversation_logger import global_conversation_logger
        from bot_agent.utils.conversation_logger import ConversationStatus

        # 创建测试会话
        test_session_id = global_conversation_logger.start_session(
            task_id="test_task",
            task_title="测试轮次编号修复",
            task_type="test",
            project_path="/test/path"
        )

        # 模拟添加多个轮次
        for i in range(5):
            global_conversation_logger.log_round(
                session_id=test_session_id,
                round_number=i + 1,
                round_name=f"测试轮次{i + 1}",
                prompt=f"测试提示{i + 1}",
                response=f"测试响应{i + 1}",
                model_name="test-model",
                duration=1.0,
                status=ConversationStatus.SUCCESS
            )

        # 结束会话
        global_conversation_logger.end_session(test_session_id, status=ConversationStatus.SUCCESS)

        # 检查轮次编号是否连续
        session = global_conversation_logger._load_session_from_file(test_session_id)
        if session and session.rounds:
            round_numbers = [r.round_number for r in session.rounds]
            expected_numbers = list(range(1, len(round_numbers) + 1))

            if round_numbers == expected_numbers:
                print("✅ 轮次编号连续性测试通过")
                print(f"  - 轮次编号: {round_numbers}")
                return True
            else:
                print(f"❌ 轮次编号不连续: {round_numbers}")
                print(f"  - 期望编号: {expected_numbers}")
                return False
        else:
            print("❌ 无法加载测试会话")
            return False

    except Exception as e:
        print(f"❌ 轮次编号修复测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_ai_enhancement():
    """测试AI修复能力增强"""
    print("\n🔧 测试AI修复能力增强...")

    try:
        from bot_agent.tools.intelligent_tool_coordinator import IntelligentToolCoordinator

        coordinator = IntelligentToolCoordinator()

        # 测试错误分析能力
        test_error = "ValueError: Error code 'Ignore' supplied to 'extend-ignore' option does not match '^[A-Z]{1,3}[0-9]{0,3}$'"

        error_type = coordinator._classify_error_type(test_error)
        error_cause = coordinator._analyze_error_cause(test_error)
        error_priority = coordinator._get_error_priority(error_type)

        print(f"  - 错误分类: {error_type}")
        print(f"  - 错误原因: {error_cause}")
        print(f"  - 修复优先级: {error_priority}")

        # 验证分析结果
        if error_type == "配置文件错误" and "flake8" in error_cause and error_priority == "高":
            print("✅ AI错误分析能力增强测试通过")
            return True
        else:
            print("❌ AI错误分析结果不符合预期")
            return False

    except Exception as e:
        print(f"❌ AI修复能力增强测试失败: {e}")
        return False

async def test_real_scenario():
    """测试真实场景下的修复效果"""
    print("\n🔧 测试真实场景修复效果...")

    try:
        from bot_agent.tools.intelligent_tool_coordinator import IntelligentToolCoordinator
        from bot_agent.tools.base_tool import ToolResult

        coordinator = IntelligentToolCoordinator()

        # 模拟真实的flake8错误
        real_errors = [
            "ValueError: Error code 'Ignore' supplied to 'extend-ignore' option does not match '^[A-Z]{1,3}[0-9]{0,3}$'",
            "E501 line too long (120 > 79 characters)",
            "F401 'os' imported but unused"
        ]

        # 测试AI修复方案生成
        project_path = "E:\\Projects\\aider-plus"

        # 这里我们只测试错误格式化，不实际调用AI
        formatted_errors = coordinator._format_errors_for_ai(real_errors)

        # 验证格式化结果包含AI工具能力说明
        checks = [
            "AI可用工具和能力清单" in formatted_errors,
            "配置文件错误" in formatted_errors,
            "flake8配置文件中包含无效字符" in formatted_errors,
            "文件操作工具" in formatted_errors,
            "多轮对话支持" in formatted_errors,
            "智能重试机制" in formatted_errors
        ]

        if all(checks):
            print("✅ 真实场景AI能力增强验证通过")
            print("  - AI工具清单已添加")
            print("  - 错误分析正确")
            print("  - 多轮对话能力已启用")
            print("  - 智能重试机制已配置")
            return True
        else:
            print("❌ 真实场景AI能力增强验证失败")
            print(f"检查结果: {checks}")
            print(f"格式化结果片段: {formatted_errors[:300]}...")
            return False

    except Exception as e:
        print(f"❌ 真实场景测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_ai_enhancement_comprehensive():
    """测试AI能力增强的综合效果"""
    print("\n🧠 测试AI能力增强综合效果...")

    try:
        from bot_agent.tools.intelligent_tool_coordinator import IntelligentToolCoordinator

        coordinator = IntelligentToolCoordinator()

        # 测试AI工具清单生成
        tools_list = coordinator._get_available_tools_for_ai()

        # 验证工具清单的完整性
        expected_tools = [
            "文件操作工具",
            "代码分析工具",
            "执行工具",
            "版本控制工具",
            "智能分析工具",
            "监控和日志工具",
            "集成工具",
            "AI增强能力",
            "多轮对话支持",
            "智能重试机制",
            "上下文学习",
            "跨平台适配"
        ]

        missing_tools = [tool for tool in expected_tools if tool not in tools_list]

        if not missing_tools:
            print("✅ AI工具清单完整性验证通过")
            print(f"  - 包含 {len(expected_tools)} 个核心工具类别")
            print("  - AI增强能力已配置")
            return True
        else:
            print("❌ AI工具清单不完整")
            print(f"缺失工具: {missing_tools}")
            return False

    except Exception as e:
        print(f"❌ AI能力增强测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_ai_continuous_inquiry():
    """测试AI持续追问机制"""
    print("\n🔄 测试AI持续追问机制...")

    try:
        from bot_agent.tools.intelligent_tool_coordinator import IntelligentToolCoordinator
        from bot_agent.engines.task_executor import AiderBasedTaskExecutor

        coordinator = IntelligentToolCoordinator()
        executor = AiderBasedTaskExecutor()

        # 验证AI持续追问机制是否存在（在两个地方都应该有）
        coordinator_has_inquiry = hasattr(coordinator, '_ai_continuous_inquiry_fix')
        executor_has_inquiry = hasattr(executor, '_ai_continuous_inquiry_fix')

        if coordinator_has_inquiry and executor_has_inquiry:
            print("✅ AI持续追问机制已在coordinator和executor中实现")

            # 检查coordinator方法签名
            import inspect
            coordinator_method = getattr(coordinator, '_ai_continuous_inquiry_fix')
            coordinator_sig = inspect.signature(coordinator_method)
            coordinator_params = list(coordinator_sig.parameters.keys())

            # 检查executor方法签名
            executor_method = getattr(executor, '_ai_continuous_inquiry_fix')
            executor_sig = inspect.signature(executor_method)
            executor_params = list(executor_sig.parameters.keys())

            # 检查参数（排除self，因为inspect可能不包含self）
            coordinator_expected = ['errors', 'project_path']
            executor_expected = ['errors', 'project_path', 'job_info']

            coordinator_ok = all(param in coordinator_params for param in coordinator_expected)
            executor_ok = all(param in executor_params for param in executor_expected)

            if coordinator_ok and executor_ok:
                print("✅ AI持续追问方法签名正确")

                # 验证系统不再依赖硬编码策略
                # 检查是否移除了硬编码的策略修复方法
                hardcoded_methods = [
                    '_multi_round_lint_fix',
                    '_multi_round_dependency_fix',
                    '_multi_round_build_fix',
                    '_multi_round_generic_fix'
                ]

                # 检查executor中的硬编码方法是否已移除
                removed_methods = []
                for method_name in hardcoded_methods:
                    if not hasattr(executor, method_name):
                        removed_methods.append(method_name)

                if len(removed_methods) == len(hardcoded_methods):
                    print("✅ 硬编码策略修复方法已完全移除")
                    print("✅ 系统现在完全依赖AI持续追问机制")
                    return True
                else:
                    print(f"⚠️ 仍有 {len(hardcoded_methods) - len(removed_methods)} 个硬编码方法未移除")
                    print(f"未移除的方法: {[m for m in hardcoded_methods if hasattr(executor, m)]}")
                    return False
            else:
                print("❌ AI持续追问方法签名不正确")
                print(f"Coordinator参数: {coordinator_params}")
                print(f"Executor参数: {executor_params}")
                return False
        else:
            print("❌ AI持续追问机制未完全实现")
            print(f"Coordinator有: {coordinator_has_inquiry}, Executor有: {executor_has_inquiry}")
            return False

    except Exception as e:
        print(f"❌ AI持续追问机制测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主测试函数"""
    print("🚀 开始核心问题修复测试\n")

    results = []

    # 测试1: ToolResult访问错误修复
    results.append(await test_toolresult_access_fix())

    # 测试2: 轮次编号修复
    results.append(await test_round_numbering_fix())

    # 测试3: AI修复能力增强
    results.append(await test_ai_enhancement())

    # 测试4: 真实场景测试
    results.append(await test_real_scenario())

    # 测试5: AI能力增强综合测试
    results.append(await test_ai_enhancement_comprehensive())

    # 测试6: AI持续追问机制测试
    results.append(await test_ai_continuous_inquiry())

    # 汇总结果
    print(f"\n📊 测试结果汇总:")
    print(f"  - 总测试数: {len(results)}")
    print(f"  - 通过数: {sum(results)}")
    print(f"  - 失败数: {len(results) - sum(results)}")
    print(f"  - 成功率: {sum(results) / len(results) * 100:.1f}%")

    if all(results):
        print("\n🎉 所有核心问题修复测试通过！")
        print("\n🔧 修复总结:")
        print("  1. ✅ ToolResult访问错误已彻底解决")
        print("  2. ✅ 轮次编号混乱问题已修复")
        print("  3. ✅ AI修复能力已大幅增强")
        print("  4. ✅ 真实场景修复效果验证通过")
        print("  5. ✅ AI工具协同能力已配置")
        print("  6. ✅ AI持续追问机制已实现")
        print("\n🚀 系统现在具备:")
        print("  - 稳定的ToolResult处理机制")
        print("  - 连续的轮次编号管理")
        print("  - 强大的AI工具协同能力")
        print("  - 智能的多轮对话支持")
        print("  - 自动的错误重试机制")
        print("  - 跨平台的命令适配")
        print("  - AI持续追问修复机制")
        print("\n🎯 核心思想实现:")
        print("  - 完全移除硬编码策略")
        print("  - AI拥有完全自主权")
        print("  - 持续追问直到解决问题")
        print("  - 充分发挥AI的智能分析能力")
        return True
    else:
        print("\n⚠️ 部分测试失败，需要进一步修复")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
