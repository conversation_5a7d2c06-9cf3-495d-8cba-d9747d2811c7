"""
测试工具 - 自动化测试执行和管理
"""

import os
import json
import subprocess
from typing import Dict, List, Optional, Any
from pathlib import Path

from .base_tool import BaseTool, ToolResult
from .terminal_tools import TerminalTools


class TestingTools(BaseTool):
    """
    测试工具

    功能：
    1. 运行单元测试
    2. 运行集成测试
    3. 代码覆盖率分析
    4. 性能测试
    5. API测试
    6. 测试报告生成
    """

    def __init__(self):
        super().__init__()
        self.terminal = TerminalTools()

    def get_capabilities(self) -> List[str]:
        """获取工具能力"""
        return [
            'run_tests',  # 通用测试执行方法
            'run_unit_tests',
            'run_integration_tests',
            'run_coverage_analysis',
            'run_performance_tests',
            'run_api_tests',
            'generate_test_report',
            'discover_tests',
            'validate_test_environment'
        ]

    def get_description(self) -> str:
        """获取工具描述"""
        return "测试工具 - 自动化测试执行、覆盖率分析、性能测试"

    async def run_unit_tests(
        self,
        project_path: str,
        test_pattern: str = "test_*.py",
        verbose: bool = True,
        parallel: bool = False
    ) -> ToolResult:
        """
        运行单元测试

        Args:
            project_path: 项目路径
            test_pattern: 测试文件模式
            verbose: 详细输出
            parallel: 并行执行

        Returns:
            ToolResult: 测试结果
        """
        try:
            self.log_operation("run_unit_tests", {
                "project_path": project_path,
                "test_pattern": test_pattern
            })

            # 构建pytest命令
            cmd_parts = ["python", "-m", "pytest"]

            if verbose:
                cmd_parts.append("-v")

            if parallel:
                cmd_parts.extend(["-n", "auto"])  # 需要pytest-xdist

            # 添加输出格式
            cmd_parts.extend([
                "--tb=short",
                "--json-report",
                "--json-report-file=test_results.json"
            ])

            # 添加测试模式
            cmd_parts.append(test_pattern)

            command = " ".join(cmd_parts)

            # 执行测试
            result = await self.terminal.execute_command(
                command,
                cwd=project_path,
                timeout=300  # 5分钟超时
            )

            # 解析测试结果
            test_data = await self._parse_test_results(project_path, result)

            return ToolResult(
                success=result.success,
                data=test_data,
                message=f"单元测试{'通过' if result.success else '失败'}",
                execution_time=result.execution_time
            )

        except Exception as e:
            self.log_error(e, "运行单元测试")
            return ToolResult(
                success=False,
                error=str(e),
                message="单元测试执行失败"
            )

    async def run_coverage_analysis(
        self,
        project_path: str,
        source_dir: str = ".",
        min_coverage: float = 80.0
    ) -> ToolResult:
        """
        运行代码覆盖率分析

        Args:
            project_path: 项目路径
            source_dir: 源代码目录
            min_coverage: 最小覆盖率要求

        Returns:
            ToolResult: 覆盖率分析结果
        """
        try:
            self.log_operation("run_coverage_analysis", {
                "project_path": project_path,
                "source_dir": source_dir,
                "min_coverage": min_coverage
            })

            # 运行覆盖率测试
            coverage_cmd = f"python -m coverage run --source={source_dir} -m pytest"
            result = await self.terminal.execute_command(
                coverage_cmd,
                cwd=project_path,
                timeout=300
            )

            if not result.success:
                return ToolResult(
                    success=False,
                    error="覆盖率测试执行失败",
                    data=result.data
                )

            # 生成覆盖率报告
            report_cmd = "python -m coverage report --format=json"
            report_result = await self.terminal.execute_command(
                report_cmd,
                cwd=project_path
            )

            # 解析覆盖率数据
            coverage_data = {}
            if report_result.success and report_result.data['stdout']:
                try:
                    coverage_data = json.loads(report_result.data['stdout'])
                except json.JSONDecodeError:
                    pass

            # 生成HTML报告
            html_cmd = "python -m coverage html -d coverage_html"
            await self.terminal.execute_command(html_cmd, cwd=project_path)

            # 分析覆盖率
            total_coverage = coverage_data.get('totals', {}).get('percent_covered', 0)
            meets_requirement = total_coverage >= min_coverage

            return ToolResult(
                success=meets_requirement,
                data={
                    'total_coverage': total_coverage,
                    'min_coverage': min_coverage,
                    'meets_requirement': meets_requirement,
                    'detailed_coverage': coverage_data,
                    'html_report_path': os.path.join(project_path, 'coverage_html', 'index.html')
                },
                message=f"代码覆盖率: {total_coverage:.1f}% ({'达标' if meets_requirement else '未达标'})"
            )

        except Exception as e:
            self.log_error(e, "覆盖率分析")
            return ToolResult(
                success=False,
                error=str(e),
                message="覆盖率分析失败"
            )

    async def run_performance_tests(
        self,
        project_path: str,
        test_file: Optional[str] = None
    ) -> ToolResult:
        """
        运行性能测试

        Args:
            project_path: 项目路径
            test_file: 特定测试文件

        Returns:
            ToolResult: 性能测试结果
        """
        try:
            self.log_operation("run_performance_tests", {
                "project_path": project_path,
                "test_file": test_file
            })

            # 使用pytest-benchmark运行性能测试
            cmd_parts = ["python", "-m", "pytest"]

            if test_file:
                cmd_parts.append(test_file)
            else:
                cmd_parts.extend(["-k", "benchmark"])

            cmd_parts.extend([
                "--benchmark-only",
                "--benchmark-json=benchmark_results.json",
                "--benchmark-sort=mean"
            ])

            command = " ".join(cmd_parts)

            result = await self.terminal.execute_command(
                command,
                cwd=project_path,
                timeout=600  # 10分钟超时
            )

            # 解析性能测试结果
            benchmark_data = {}
            benchmark_file = os.path.join(project_path, "benchmark_results.json")
            if os.path.exists(benchmark_file):
                try:
                    with open(benchmark_file, 'r') as f:
                        benchmark_data = json.load(f)
                except:
                    pass

            return ToolResult(
                success=result.success,
                data={
                    'benchmark_results': benchmark_data,
                    'test_output': result.data['stdout'] if result.data else None
                },
                message=f"性能测试{'完成' if result.success else '失败'}",
                execution_time=result.execution_time
            )

        except Exception as e:
            self.log_error(e, "性能测试")
            return ToolResult(
                success=False,
                error=str(e),
                message="性能测试失败"
            )

    async def run_api_tests(
        self,
        project_path: str,
        base_url: str,
        test_spec: Optional[str] = None
    ) -> ToolResult:
        """
        运行API测试

        Args:
            project_path: 项目路径
            base_url: API基础URL
            test_spec: 测试规范文件

        Returns:
            ToolResult: API测试结果
        """
        try:
            self.log_operation("run_api_tests", {
                "project_path": project_path,
                "base_url": base_url,
                "test_spec": test_spec
            })

            # 设置环境变量
            env = {"API_BASE_URL": base_url}

            if test_spec and os.path.exists(os.path.join(project_path, test_spec)):
                # 使用指定的测试规范
                command = f"python -m pytest {test_spec} -v --tb=short"
            else:
                # 运行所有API测试
                command = "python -m pytest -k 'api or test_api' -v --tb=short"

            result = await self.terminal.execute_command(
                command,
                cwd=project_path,
                timeout=300,
                env=env
            )

            return ToolResult(
                success=result.success,
                data={
                    'base_url': base_url,
                    'test_output': result.data['stdout'] if result.data else None,
                    'test_errors': result.data['stderr'] if result.data else None
                },
                message=f"API测试{'通过' if result.success else '失败'}",
                execution_time=result.execution_time
            )

        except Exception as e:
            self.log_error(e, "API测试")
            return ToolResult(
                success=False,
                error=str(e),
                message="API测试失败"
            )

    async def discover_tests(self, project_path: str) -> ToolResult:
        """
        发现项目中的测试

        Args:
            project_path: 项目路径

        Returns:
            ToolResult: 测试发现结果
        """
        try:
            self.log_operation("discover_tests", {"project_path": project_path})

            # 使用pytest收集测试
            command = "python -m pytest --collect-only -q"
            result = await self.terminal.execute_command(
                command,
                cwd=project_path
            )

            # 查找测试文件
            test_files = []
            test_dirs = []

            for root, dirs, files in os.walk(project_path):
                # 跳过隐藏目录和常见忽略目录
                dirs[:] = [d for d in dirs if not d.startswith('.') and d not in ['__pycache__', 'node_modules']]

                for file in files:
                    if (file.startswith('test_') or file.endswith('_test.py')) and file.endswith('.py'):
                        test_files.append(os.path.relpath(os.path.join(root, file), project_path))

                if any(f.startswith('test_') for f in files):
                    test_dirs.append(os.path.relpath(root, project_path))

            return ToolResult(
                success=True,
                data={
                    'test_files': test_files,
                    'test_directories': test_dirs,
                    'total_test_files': len(test_files),
                    'pytest_output': result.data['stdout'] if result.data else None
                },
                message=f"发现 {len(test_files)} 个测试文件"
            )

        except Exception as e:
            self.log_error(e, "测试发现")
            return ToolResult(
                success=False,
                error=str(e),
                message="测试发现失败"
            )

    async def validate_test_environment(self, project_path: str) -> ToolResult:
        """
        验证测试环境

        Args:
            project_path: 项目路径

        Returns:
            ToolResult: 环境验证结果
        """
        try:
            self.log_operation("validate_test_environment", {"project_path": project_path})

            validation_results = {}

            # 检查pytest是否安装
            pytest_check = await self.terminal.execute_command("python -m pytest --version")
            validation_results['pytest_installed'] = pytest_check.success

            # 检查coverage是否安装
            coverage_check = await self.terminal.execute_command("python -m coverage --version")
            validation_results['coverage_installed'] = coverage_check.success

            # 检查测试目录是否存在
            test_dirs = ['tests', 'test']
            validation_results['test_directories'] = []
            for test_dir in test_dirs:
                test_path = os.path.join(project_path, test_dir)
                if os.path.exists(test_path):
                    validation_results['test_directories'].append(test_dir)

            # 检查配置文件
            config_files = ['pytest.ini', 'pyproject.toml', 'setup.cfg', 'tox.ini']
            validation_results['config_files'] = []
            for config_file in config_files:
                config_path = os.path.join(project_path, config_file)
                if os.path.exists(config_path):
                    validation_results['config_files'].append(config_file)

            # 计算环境健康度
            health_score = 0
            if validation_results['pytest_installed']:
                health_score += 40
            if validation_results['coverage_installed']:
                health_score += 20
            if validation_results['test_directories']:
                health_score += 30
            if validation_results['config_files']:
                health_score += 10

            validation_results['health_score'] = health_score
            validation_results['is_ready'] = health_score >= 70

            return ToolResult(
                success=True,
                data=validation_results,
                message=f"测试环境健康度: {health_score}% ({'就绪' if validation_results['is_ready'] else '需要配置'})"
            )

        except Exception as e:
            self.log_error(e, "测试环境验证")
            return ToolResult(
                success=False,
                error=str(e),
                message="测试环境验证失败"
            )

    async def _parse_test_results(self, project_path: str, test_result: ToolResult) -> Dict:
        """解析测试结果"""
        # 安全地提取ToolResult数据
        test_data = test_result.data if test_result.data and isinstance(test_result.data, dict) else {}

        data = {
            'raw_output': test_data.get('stdout', None),
            'errors': test_data.get('stderr', None),
            'return_code': test_data.get('return_code', None)
        }

        # 尝试解析JSON报告
        json_report_path = os.path.join(project_path, "test_results.json")
        if os.path.exists(json_report_path):
            try:
                with open(json_report_path, 'r') as f:
                    json_data = json.load(f)
                    data['json_report'] = json_data
                    data['summary'] = json_data.get('summary', {})
            except:
                pass

        return data

    async def run_tests(self, project_path: str, **kwargs) -> ToolResult:
        """
        运行测试 - 通用测试执行方法

        Args:
            project_path: 项目路径
            **kwargs: 其他参数

        Returns:
            ToolResult: 测试结果
        """
        try:
            self.log_operation("run_tests", {"project_path": project_path})

            # 默认运行单元测试
            return await self.run_unit_tests(project_path, **kwargs)

        except Exception as e:
            self.log_error(e, "运行测试")
            return ToolResult(
                success=False,
                error=str(e),
                message="测试执行失败"
            )

    async def execute(self, *args, **kwargs) -> ToolResult:
        """执行工具操作"""
        # 默认运行单元测试
        if args:
            return await self.run_unit_tests(args[0], **kwargs)
        else:
            return ToolResult(
                success=False,
                error="缺少项目路径参数"
            )
